# Google Sheets File Links Handler

Tiện ích Chrome Extension giúp chuyển đổi các liên kết file:/// trong Google Sheets thành các liên kết có thể nhấp được và mở file trực tiếp trên máy tính.

## 🚀 Tính năng

- ✅ Tự động phát hiện và chuyển đổi các đường dẫn file:/// trong Google Sheets
- ✅ Tạo liên kết có thể nhấp được với styling đẹp mắt
- ✅ Mở file trực tiếp bằng ứng dụng mặc định của hệ điều hành
- ✅ Hỗ trợ tất cả định dạng file (PDF, Word, Excel, PowerPoint, v.v.)
- ✅ Giao diện popup để cấu hình và kiểm tra
- ✅ Thông báo trạng thái khi mở file
- ✅ Hỗ trợ dark mode và responsive design

## 📋 Yêu cầu hệ thống

- Google Chrome phiên bản 88 trở lên
- Windows 10/11 (đã test)
- Quyền truy cập vào Google Sheets

## 🔧 Cài đặt

### Cách 1: Cài đặt từ source code

1. **Tải source code:**
   ```bash
   git clone [repository-url]
   cd google-sheets-file-links-extension
   ```

2. **Mở Chrome Extensions:**
   - Mở Chrome và truy cập `chrome://extensions/`
   - Bật "Developer mode" ở góc trên bên phải

3. **Load extension:**
   - Nhấp "Load unpacked"
   - Chọn thư mục `google-sheets-file-links-extension`
   - Extension sẽ được cài đặt và kích hoạt

### Cách 2: Cài đặt từ file .crx (nếu có)

1. Tải file `.crx` về máy
2. Kéo thả file vào trang `chrome://extensions/`
3. Xác nhận cài đặt

## 📖 Hướng dẫn sử dụng

### Bước 1: Chuẩn bị đường dẫn file

Trong Google Sheets, nhập đường dẫn file theo định dạng:
```
file:///C:/path/to/your/file.pdf
file:///D:/Documents/report.docx
file:///C:/Users/<USER>/Desktop/data.xlsx
file:///G:/My Drive/2. Cá nhân/6. Test viết code/document.pdf
file:///C:/Program Files/Microsoft Office/WINWORD.EXE
```

### Bước 2: Extension tự động xử lý

- Extension sẽ tự động phát hiện các đường dẫn file:///
- Chuyển đổi chúng thành liên kết màu xanh có thể nhấp được
- Hiển thị tooltip khi hover chuột
- **Hỗ trợ đường dẫn có dấu cách và ký tự đặc biệt**

### Bước 3: Nhấp để mở file

- Nhấp vào liên kết để mở file
- File sẽ được mở bằng ứng dụng mặc định
- Thông báo sẽ hiển thị trạng thái

## ⚙️ Cấu hình

Nhấp vào icon extension trên thanh công cụ để:

- **Bật/tắt extension:** Toggle chính để kích hoạt/vô hiệu hóa
- **Bật/tắt thông báo:** Hiển thị thông báo khi mở file
- **Làm mới:** Reload tất cả tab Google Sheets
- **Kiểm tra:** Test chức năng extension

## 🔍 Troubleshooting

### Extension không hoạt động

1. **Kiểm tra quyền:**
   - Đảm bảo extension có quyền truy cập Google Sheets
   - Refresh trang Google Sheets

2. **Kiểm tra định dạng đường dẫn:**
   ```
   ✅ Đúng: file:///C:/Documents/file.pdf
   ✅ Đúng: file:///G:/My Drive/folder/file.pdf
   ✅ Đúng: file:///C:/Program Files/app.exe
   ❌ Sai: C:\Documents\file.pdf
   ❌ Sai: file://C:/Documents/file.pdf
   ```

3. **Vấn đề với đường dẫn có dấu cách:**
   - Extension đã được cải tiến để hỗ trợ dấu cách
   - Nếu vẫn không hoạt động, thử reload extension
   - Sử dụng file `test_spaces.html` để test

4. **Kiểm tra file tồn tại:**
   - Đảm bảo file thực sự tồn tại trên máy tính
   - Kiểm tra quyền truy cập file

### File không mở được

1. **Kiểm tra ứng dụng mặc định:**
   - Đảm bảo có ứng dụng để mở loại file đó
   - Thử mở file thủ công để test

2. **Kiểm tra đường dẫn:**
   - Copy đường dẫn và paste vào File Explorer
   - Đảm bảo không có ký tự đặc biệt

3. **Kiểm tra quyền bảo mật:**
   - Một số file có thể bị chặn bởi antivirus
   - Kiểm tra Windows Security settings

## 🛠️ Development

### Cấu trúc project

```
google-sheets-file-links-extension/
├── manifest.json          # Cấu hình extension
├── content.js            # Script chạy trên Google Sheets
├── background.js         # Service worker
├── popup.html           # Giao diện popup
├── popup.js             # Logic popup
├── popup.css            # Styling popup
├── styles.css           # Styling cho content script
├── icons/               # Icons cho extension
└── README.md           # Tài liệu này
```

### Build và test

1. **Load extension trong development mode:**
   ```bash
   # Mở Chrome Extensions
   chrome://extensions/
   
   # Bật Developer mode
   # Load unpacked -> chọn thư mục project
   ```

2. **Debug:**
   - Content script: F12 trên Google Sheets
   - Background script: chrome://extensions/ -> Inspect views
   - Popup: Right-click icon -> Inspect popup

3. **Test:**
   - Tạo Google Sheets mới
   - Nhập đường dẫn file:/// test
   - Kiểm tra chức năng

## 📝 Changelog

### v1.0.0 (2024-01-XX)
- ✨ Phiên bản đầu tiên
- ✨ Hỗ trợ chuyển đổi file:/// links
- ✨ Popup cấu hình
- ✨ Thông báo trạng thái
- ✨ Dark mode support

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Mở Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Hỗ trợ

Nếu gặp vấn đề hoặc có câu hỏi:

1. Kiểm tra [Troubleshooting](#-troubleshooting)
2. Tạo issue trên GitHub
3. Liên hệ developer

## 🙏 Credits

- Developed with ❤️ for Vietnamese users
- Icons from Google Material Design
- Built with Chrome Extensions Manifest V3
