/* Popup CSS cho Google Sheets File Links Handler */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.5;
}

.container {
    width: 380px;
    max-height: 600px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #4285f4, #34a853);
    color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.icon {
    width: 32px;
    height: 32px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
}

.content {
    padding: 20px;
}

.setting-group {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.setting-info h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.setting-info p {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    flex-shrink: 0;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4285f4;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.divider {
    height: 1px;
    background: #e9ecef;
    margin: 20px 0;
}

.info-section {
    margin-bottom: 20px;
}

.info-section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.info-section ol {
    padding-left: 20px;
    font-size: 12px;
    color: #666;
}

.info-section li {
    margin-bottom: 5px;
}

.example {
    background: #f1f3f4;
    padding: 10px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    color: #333;
    line-height: 1.6;
}

code {
    background: #e8f0fe;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    color: #1a73e8;
}

.status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: #e8f5e8;
    border-radius: 6px;
    font-size: 12px;
    margin-bottom: 15px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #34a853;
}

.status.error {
    background: #fce8e6;
}

.status.error .status-indicator {
    background: #ea4335;
}

.footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #4285f4;
    color: white;
}

.btn-primary:hover {
    background: #3367d6;
}

.btn-secondary {
    background: #f8f9fa;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.btn-secondary:hover {
    background: #f1f3f4;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Scrollbar styling */
.container::-webkit-scrollbar {
    width: 6px;
}

.container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
