#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script tạo shortcuts để chạy ứng dụng mà không hiển thị terminal
"""

import os
import sys
import win32com.client
from pathlib import Path

def create_shortcut(target_path, shortcut_path, description="", icon_path=""):
    """Tạo shortcut Windows"""
    try:
        shell = win32com.client.Dispatch("WScript.Shell")
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = target_path
        shortcut.Description = description
        shortcut.WorkingDirectory = os.path.dirname(target_path)
        
        if icon_path and os.path.exists(icon_path):
            shortcut.IconLocation = icon_path
            
        shortcut.save()
        return True, f"Đã tạo shortcut: {shortcut_path}"
    except Exception as e:
        return False, f"Lỗi tạo shortcut: {e}"

def main():
    """Main function"""
    print("🔗 TẠO SHORTCUTS CHO ỨNG DỤNG")
    print("=" * 50)
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    desktop = os.path.join(os.path.expanduser("~"), "Desktop")
    
    # Tìm pythonw.exe
    python_exe = sys.executable
    pythonw_exe = python_exe.replace('python.exe', 'pythonw.exe')
    
    if not os.path.exists(pythonw_exe):
        print(f"❌ Không tìm thấy pythonw.exe: {pythonw_exe}")
        print("Sử dụng python.exe thay thế")
        pythonw_exe = python_exe
    else:
        print(f"✅ Tìm thấy pythonw.exe: {pythonw_exe}")
    
    # Danh sách shortcuts cần tạo
    shortcuts = [
        {
            'name': 'Dự án đọc tài liệu - Launcher',
            'target': f'"{pythonw_exe}" "{os.path.join(script_dir, "project_launcher.pyw")}"',
            'description': 'Project Launcher - Quản lý dự án đọc tài liệu',
            'filename': 'Dự án đọc tài liệu - Launcher.lnk'
        },
        {
            'name': 'Dự án đọc tài liệu - Main App',
            'target': f'"{pythonw_exe}" "{os.path.join(script_dir, "main.pyw")}"',
            'description': 'Ứng dụng chính - PDF OCR và Tóm tắt',
            'filename': 'Dự án đọc tài liệu - Main.lnk'
        },
        {
            'name': 'Dự án đọc tài liệu - Config',
            'target': f'"{pythonw_exe}" "{os.path.join(script_dir, "config_manager.py")}"',
            'description': 'Quản lý cấu hình API và Google Sheet',
            'filename': 'Dự án đọc tài liệu - Config.lnk'
        },
        {
            'name': 'Dự án đọc tài liệu - Context Menu',
            'target': f'"{pythonw_exe}" "{os.path.join(script_dir, "register_pdf_context_menu.py")}"',
            'description': 'Đăng ký menu chuột phải cho PDF',
            'filename': 'Dự án đọc tài liệu - Context Menu.lnk'
        }
    ]
    
    success_count = 0
    
    for shortcut_info in shortcuts:
        shortcut_path = os.path.join(desktop, shortcut_info['filename'])
        
        print(f"\n📎 Tạo shortcut: {shortcut_info['name']}")
        print(f"   Target: {shortcut_info['target']}")
        print(f"   Path: {shortcut_path}")
        
        success, message = create_shortcut(
            shortcut_info['target'],
            shortcut_path,
            shortcut_info['description']
        )
        
        if success:
            print(f"   ✅ {message}")
            success_count += 1
        else:
            print(f"   ❌ {message}")
    
    print("\n" + "=" * 50)
    print(f"📊 KẾT QUẢ: Đã tạo {success_count}/{len(shortcuts)} shortcuts")
    
    if success_count > 0:
        print(f"✅ Các shortcuts đã được tạo trên Desktop")
        print("🎯 Bây giờ bạn có thể double-click vào shortcuts để chạy ứng dụng mà không hiển thị terminal!")
    else:
        print("❌ Không thể tạo shortcuts. Kiểm tra quyền truy cập.")
    
    # Tạo thêm shortcuts trong thư mục dự án
    print(f"\n📁 Tạo shortcuts trong thư mục dự án...")
    project_shortcuts_dir = os.path.join(script_dir, "Shortcuts")
    
    try:
        os.makedirs(project_shortcuts_dir, exist_ok=True)
        print(f"✅ Đã tạo thư mục: {project_shortcuts_dir}")
        
        for shortcut_info in shortcuts:
            shortcut_path = os.path.join(project_shortcuts_dir, shortcut_info['filename'])
            success, message = create_shortcut(
                shortcut_info['target'],
                shortcut_path,
                shortcut_info['description']
            )
            
            if success:
                print(f"   ✅ {shortcut_info['filename']}")
            else:
                print(f"   ❌ {shortcut_info['filename']}: {message}")
                
    except Exception as e:
        print(f"❌ Lỗi tạo thư mục shortcuts: {e}")
    
    print(f"\n🎉 HOÀN THÀNH!")
    input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")
