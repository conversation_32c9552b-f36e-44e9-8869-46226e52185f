/* Popup CSS cho Google Drive PDF Downloader */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8f9fa;
    color: #202124;
    line-height: 1.4;
}

.container {
    width: 380px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Header */
.header {
    background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
    color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.icon svg {
    filter: brightness(0) invert(1);
}

.title h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 2px;
}

.title p {
    font-size: 13px;
    opacity: 0.9;
}

/* Content */
.content {
    padding: 20px;
}

/* Status */
.status {
    text-align: center;
    padding: 20px 0;
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: #5f6368;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e8eaed;
    border-top: 2px solid #1a73e8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* File Info */
.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.file-icon svg {
    opacity: 0.8;
}

.file-details {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    font-size: 14px;
    color: #202124;
    margin-bottom: 4px;
    word-break: break-word;
}

.file-id {
    font-size: 12px;
    color: #5f6368;
    font-family: monospace;
    word-break: break-all;
}

/* Actions */
.actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.download-btn {
    background: #1a73e8;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.download-btn:hover:not(:disabled) {
    background: #1557b0;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
}

.download-btn:disabled {
    background: #e8eaed;
    color: #9aa0a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.secondary-btn {
    background: white;
    color: #1a73e8;
    border: 1px solid #dadce0;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.secondary-btn:hover:not(:disabled) {
    background: #f8f9fa;
    border-color: #1a73e8;
}

.secondary-btn:disabled {
    background: #f8f9fa;
    color: #9aa0a6;
    border-color: #e8eaed;
    cursor: not-allowed;
}

/* Message */
.message {
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 16px;
}

.message.success {
    background: #e8f5e8;
    color: #137333;
    border: 1px solid #ceead6;
}

.message.error {
    background: #fce8e6;
    color: #d93025;
    border: 1px solid #f9dedc;
}

.message.info {
    background: #e8f0fe;
    color: #1a73e8;
    border: 1px solid #d2e3fc;
}

.message.warning {
    background: #fef7e0;
    color: #ea8600;
    border: 1px solid #fdd663;
}

/* Footer */
.footer {
    padding: 16px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e8eaed;
}

.help-text {
    margin-bottom: 8px;
}

.help-text small {
    color: #5f6368;
    font-size: 12px;
}

.links {
    display: flex;
    align-items: center;
    gap: 8px;
}

.links a {
    color: #1a73e8;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
}

.links a:hover {
    text-decoration: underline;
}

.links span {
    color: #dadce0;
    font-size: 12px;
}

/* Responsive */
@media (max-width: 400px) {
    .container {
        width: 100%;
        border-radius: 0;
    }
    
    .header {
        padding: 16px;
    }
    
    .content {
        padding: 16px;
    }
    
    .footer {
        padding: 12px 16px;
    }
}
