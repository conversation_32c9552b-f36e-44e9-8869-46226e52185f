# Changelog - Google Sheets File Links Handler

Tất cả các thay đổi quan trọng của dự án sẽ được ghi lại trong file này.

## [1.1.0] - 2024-01-XX (<PERSON><PERSON><PERSON> tiến hỗ trợ dấu cách)

### ✨ Added
- Hỗ trợ đầy đủ đường dẫn file có dấu cách và ký tự đặc biệt
- File `test_spaces.html` để test các trường hợp phức tạp
- Regex cải tiến với nhiều pattern matching
- Phương thức `escapeHtml()` để xử lý an toàn HTML
- Thuật toán thay thế text thông minh với placeholder

### 🔧 Changed
- Cải tiến `extractFileLinks()` với regex mạnh mẽ hơn
- Cải tiến `makeLinksClickable()` với xử lý placeholder
- Sắ<PERSON> xếp links theo độ dài để tránh thay thế nhầm
- <PERSON>ọ<PERSON> và deduplicate matches tốt hơn

### 🐛 Fixed
- **Lỗi chính:** Extension chỉ chuyển đổi một phần đường dẫn có dấu cách
- Lỗi thay thế text không chính xác
- Lỗi regex không match đường dẫn dài
- Lỗi HTML injection khi có ký tự đặc biệt

### 📝 Documentation
- Cập nhật README với ví dụ đường dẫn có dấu cách
- Thêm troubleshooting cho vấn đề dấu cách
- Hướng dẫn test với file `test_spaces.html`

### 🧪 Testing
- Test cases cho đường dẫn có dấu cách
- Test cases cho ký tự đặc biệt Tiếng Việt
- Test cases cho đường dẫn dài và phức tạp
- Regex testing tool trong `test_spaces.html`

## [1.0.0] - 2024-01-XX (Phiên bản đầu tiên)

### ✨ Added
- Tính năng chính: Chuyển đổi file:/// links thành clickable links
- Content script để xử lý Google Sheets
- Background script để mở files
- Popup UI để cấu hình extension
- Styling đẹp mắt cho file links
- Thông báo trạng thái khi mở file
- Hỗ trợ dark mode và responsive design

### 📁 Files
- `manifest.json` - Cấu hình extension
- `content.js` - Script chạy trên Google Sheets
- `background.js` - Service worker
- `popup.html/js/css` - Giao diện popup
- `styles.css` - Styling cho links
- `README.md` - Tài liệu
- `INSTALLATION_GUIDE.md` - Hướng dẫn cài đặt
- `demo.html` - Trang demo
- `create_icons.html` - Tool tạo icons

### 🎯 Features
- Tự động phát hiện file:/// patterns
- Click để mở file với ứng dụng mặc định
- Tooltip hiển thị đường dẫn đầy đủ
- Cấu hình bật/tắt extension
- Kiểm tra trạng thái extension
- Thông báo success/error

---

## 🔄 Migration Guide

### Từ v1.0.0 lên v1.1.0

Không cần thay đổi gì, chỉ cần:

1. **Cập nhật files:**
   ```bash
   # Thay thế file content.js mới
   # Thêm file test_spaces.html
   # Cập nhật README.md
   ```

2. **Reload extension:**
   - Vào chrome://extensions/
   - Nhấp nút reload trên extension

3. **Test lại:**
   - Mở `test_spaces.html` để test
   - Thử với đường dẫn có dấu cách
   - Kiểm tra popup vẫn hoạt động

### Backward Compatibility

- ✅ Tương thích ngược hoàn toàn
- ✅ Cài đặt cũ được giữ nguyên
- ✅ Không cần cấu hình lại

---

## 🐛 Known Issues

### v1.1.0
- Một số trường hợp edge case với ký tự Unicode phức tạp
- Performance có thể chậm với text rất dài (>10000 ký tự)

### v1.0.0
- ❌ **Đã sửa:** Không hỗ trợ đường dẫn có dấu cách
- ❌ **Đã sửa:** Regex không match đường dẫn dài

---

## 📋 Roadmap

### v1.2.0 (Planned)
- [ ] Hỗ trợ drag & drop files vào Google Sheets
- [ ] Context menu để convert text thành file links
- [ ] Batch processing cho nhiều links
- [ ] Export/import settings

### v1.3.0 (Future)
- [ ] Hỗ trợ network paths (\\server\share)
- [ ] Preview file content trong tooltip
- [ ] Integration với cloud storage
- [ ] Keyboard shortcuts

---

## 🤝 Contributing

Để đóng góp vào dự án:

1. **Report bugs:** Tạo issue với template bug report
2. **Suggest features:** Tạo issue với template feature request
3. **Submit PR:** Fork → Branch → Commit → PR
4. **Test:** Sử dụng `test_spaces.html` để test

### Development Setup

```bash
# Clone repository
git clone [repo-url]
cd google-sheets-file-links-extension

# Load extension trong Chrome
# chrome://extensions/ → Developer mode → Load unpacked

# Test với file test
open test_spaces.html
```

---

## 📞 Support

- **GitHub Issues:** [Link to issues]
- **Documentation:** README.md, INSTALLATION_GUIDE.md
- **Testing:** test_spaces.html, demo.html
- **Email:** [Your email]

---

*Cảm ơn bạn đã sử dụng Google Sheets File Links Handler! 🙏*
