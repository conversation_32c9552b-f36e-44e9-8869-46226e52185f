// Content script cho Google Drive PDF Downloader

// Biến toàn cục
let currentFileId = null;
let currentFileName = null;
let downloadButton = null;

// Khởi tạo khi trang được tải
function initialize() {
  // Lấy file ID từ URL
  currentFileId = extractFileIdFromUrl();
  
  if (currentFileId) {
    // Lấy tên file
    currentFileName = extractFileName();
    
    // Tạo nút tải xuống
    createDownloadButton();
    
    // Theo dõi thay đổi URL (cho single page applications)
    observeUrlChanges();
  }
}

// Trích xuất file ID từ URL
function extractFileIdFromUrl() {
  const url = window.location.href;
  const match = url.match(/\/file\/d\/([a-zA-Z0-9-_]+)/);
  return match ? match[1] : null;
}

// Trích xuất tên file từ trang
function extractFileName() {
  // Thử các selector khác nhau để lấy tên file
  const selectors = [
    '[data-tooltip="Đổi tên"]', // Nút đổi tên
    '.ndfHFb-c4YZDc-Wrql6b', // Tiêu đề file
    'h1[data-tooltip]',
    '.ndfHFb-c4YZDc-Wrql6b-LgbsSe-BPrWId',
    'span[role="button"][data-tooltip*="tên"]'
  ];
  
  for (const selector of selectors) {
    const element = document.querySelector(selector);
    if (element) {
      let fileName = element.textContent || element.getAttribute('data-tooltip') || '';
      fileName = fileName.trim();
      if (fileName && !fileName.includes('Đổi tên')) {
        // Đảm bảo có đuôi .pdf
        if (!fileName.toLowerCase().endsWith('.pdf')) {
          fileName += '.pdf';
        }
        return fileName;
      }
    }
  }
  
  // Fallback: sử dụng title của trang
  const title = document.title;
  if (title && title !== 'Google Drive') {
    let fileName = title.replace(' - Google Drive', '').trim();
    if (!fileName.toLowerCase().endsWith('.pdf')) {
      fileName += '.pdf';
    }
    return fileName;
  }
  
  return `google-drive-${currentFileId}.pdf`;
}

// Tạo nút tải xuống
function createDownloadButton() {
  // Kiểm tra xem nút đã tồn tại chưa
  if (downloadButton) {
    downloadButton.remove();
  }
  
  // Tạo nút mới
  downloadButton = document.createElement('button');
  downloadButton.innerHTML = `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
    </svg>
    Tải PDF
  `;
  downloadButton.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    background: #1a73e8;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    font-family: 'Google Sans', Roboto, sans-serif;
  `;
  
  // Hiệu ứng hover
  downloadButton.addEventListener('mouseenter', () => {
    downloadButton.style.background = '#1557b0';
    downloadButton.style.transform = 'translateY(-1px)';
    downloadButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
  });
  
  downloadButton.addEventListener('mouseleave', () => {
    downloadButton.style.background = '#1a73e8';
    downloadButton.style.transform = 'translateY(0)';
    downloadButton.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
  });
  
  // Xử lý click
  downloadButton.addEventListener('click', handleDownloadClick);
  
  // Thêm vào trang
  document.body.appendChild(downloadButton);
}

// Xử lý click nút tải xuống
async function handleDownloadClick() {
  if (!currentFileId) {
    showNotification('Không thể xác định file ID', 'error');
    return;
  }

  // Kiểm tra quyền truy cập trước
  const hasPermission = await checkFilePermission();
  if (!hasPermission) {
    showPermissionDialog();
    return;
  }

  // Hiển thị trạng thái loading
  const originalText = downloadButton.innerHTML;
  downloadButton.innerHTML = `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="spinning">
      <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z"/>
    </svg>
    Đang tải...
  `;
  downloadButton.disabled = true;

  // Thêm CSS cho animation xoay
  if (!document.querySelector('#spinning-style')) {
    const style = document.createElement('style');
    style.id = 'spinning-style';
    style.textContent = `
      .spinning {
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
  }

  try {
    // Gửi yêu cầu tải xuống đến background script
    const response = await chrome.runtime.sendMessage({
      action: 'downloadPDF',
      fileId: currentFileId,
      fileName: currentFileName
    });

    if (response && response.success) {
      showNotification('Bắt đầu tải xuống PDF', 'success');
    } else {
      showNotification('Không thể tải xuống. Thử phương pháp khác...', 'warning');
      // Thử phương pháp thay thế
      tryAlternativeMethods();
    }
  } catch (error) {
    console.error('Lỗi:', error);
    showNotification('Lỗi tải xuống. Thử phương pháp khác...', 'warning');
    tryAlternativeMethods();
  } finally {
    // Khôi phục nút
    setTimeout(() => {
      downloadButton.innerHTML = originalText;
      downloadButton.disabled = false;
    }, 2000);
  }
}

// Hiển thị thông báo
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 10001;
    background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    transform: translateX(100%);
    font-family: 'Google Sans', Roboto, sans-serif;
  `;
  
  document.body.appendChild(notification);
  
  // Animation hiển thị
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
  }, 100);
  
  // Tự động ẩn sau 3 giây
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

// Theo dõi thay đổi URL
function observeUrlChanges() {
  let lastUrl = location.href;
  new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
      lastUrl = url;
      // URL đã thay đổi, khởi tạo lại
      setTimeout(initialize, 1000);
    }
  }).observe(document, {subtree: true, childList: true});
}

// Kiểm tra quyền truy cập file
async function checkFilePermission() {
  // Kiểm tra xem có thông báo lỗi quyền truy cập không
  const errorMessages = [
    'Sorry, the owner hasn\'t given you permission',
    'Only the owner and editors can download',
    'You need permission',
    'Access denied'
  ];

  const pageText = document.body.innerText;
  return !errorMessages.some(msg => pageText.includes(msg));
}

// Hiển thị dialog cho trường hợp không có quyền
function showPermissionDialog() {
  const dialog = document.createElement('div');
  dialog.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10002;
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    max-width: 400px;
    font-family: 'Google Sans', Roboto, sans-serif;
  `;

  dialog.innerHTML = `
    <div style="text-align: center;">
      <div style="color: #ea4335; font-size: 48px; margin-bottom: 16px;">⚠️</div>
      <h3 style="margin: 0 0 12px 0; color: #202124;">File bị hạn chế tải xuống</h3>
      <p style="margin: 0 0 20px 0; color: #5f6368; line-height: 1.4;">
        Chủ sở hữu file đã hạn chế quyền tải xuống. Bạn có thể thử các phương pháp sau:
      </p>
      <div style="text-align: left; margin-bottom: 20px;">
        <div style="margin-bottom: 8px;">• In thành PDF (Ctrl+P)</div>
        <div style="margin-bottom: 8px;">• Sao chép nội dung</div>
        <div style="margin-bottom: 8px;">• Chụp ảnh màn hình</div>
        <div>• Liên hệ chủ sở hữu</div>
      </div>
      <div style="display: flex; gap: 12px; justify-content: center;">
        <button id="print-pdf-btn" style="
          background: #1a73e8; color: white; border: none; border-radius: 6px;
          padding: 8px 16px; cursor: pointer; font-weight: 500;
        ">In thành PDF</button>
        <button id="close-dialog-btn" style="
          background: #f8f9fa; color: #3c4043; border: 1px solid #dadce0;
          border-radius: 6px; padding: 8px 16px; cursor: pointer; font-weight: 500;
        ">Đóng</button>
      </div>
    </div>
  `;

  // Thêm backdrop
  const backdrop = document.createElement('div');
  backdrop.style.cssText = `
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 10001;
  `;

  document.body.appendChild(backdrop);
  document.body.appendChild(dialog);

  // Event listeners
  dialog.querySelector('#print-pdf-btn').addEventListener('click', () => {
    window.print();
    document.body.removeChild(backdrop);
    document.body.removeChild(dialog);
  });

  dialog.querySelector('#close-dialog-btn').addEventListener('click', () => {
    document.body.removeChild(backdrop);
    document.body.removeChild(dialog);
  });

  backdrop.addEventListener('click', () => {
    document.body.removeChild(backdrop);
    document.body.removeChild(dialog);
  });
}

// Thử các phương pháp thay thế
function tryAlternativeMethods() {
  const alternativeDialog = document.createElement('div');
  alternativeDialog.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10002;
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    max-width: 450px;
    font-family: 'Google Sans', Roboto, sans-serif;
  `;

  alternativeDialog.innerHTML = `
    <div style="text-align: center;">
      <div style="color: #fbbc04; font-size: 48px; margin-bottom: 16px;">💡</div>
      <h3 style="margin: 0 0 12px 0; color: #202124;">Thử phương pháp khác</h3>
      <p style="margin: 0 0 20px 0; color: #5f6368; line-height: 1.4;">
        Tải xuống trực tiếp không thành công. Hãy thử các cách sau:
      </p>
      <div style="display: flex; flex-direction: column; gap: 12px; margin-bottom: 20px;">
        <button id="print-method-btn" style="
          background: #1a73e8; color: white; border: none; border-radius: 6px;
          padding: 12px 16px; cursor: pointer; font-weight: 500;
        ">🖨️ In thành PDF (Ctrl+P)</button>
        <button id="copy-link-btn" style="
          background: #34a853; color: white; border: none; border-radius: 6px;
          padding: 12px 16px; cursor: pointer; font-weight: 500;
        ">🔗 Sao chép link tải xuống</button>
        <button id="open-new-tab-btn" style="
          background: #ea4335; color: white; border: none; border-radius: 6px;
          padding: 12px 16px; cursor: pointer; font-weight: 500;
        ">🌐 Mở tab mới để thử tải</button>
      </div>
      <button id="close-alt-dialog-btn" style="
        background: #f8f9fa; color: #3c4043; border: 1px solid #dadce0;
        border-radius: 6px; padding: 8px 16px; cursor: pointer; font-weight: 500;
      ">Đóng</button>
    </div>
  `;

  // Thêm backdrop
  const backdrop = document.createElement('div');
  backdrop.style.cssText = `
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 10001;
  `;

  document.body.appendChild(backdrop);
  document.body.appendChild(alternativeDialog);

  // Event listeners
  alternativeDialog.querySelector('#print-method-btn').addEventListener('click', () => {
    window.print();
    closeDialog();
  });

  alternativeDialog.querySelector('#copy-link-btn').addEventListener('click', async () => {
    const downloadUrl = `https://drive.google.com/uc?export=download&id=${currentFileId}`;
    try {
      await navigator.clipboard.writeText(downloadUrl);
      showNotification('Đã sao chép link tải xuống', 'success');
    } catch (error) {
      showNotification('Không thể sao chép link', 'error');
    }
    closeDialog();
  });

  alternativeDialog.querySelector('#open-new-tab-btn').addEventListener('click', () => {
    window.open(`https://drive.google.com/uc?export=download&id=${currentFileId}`, '_blank');
    closeDialog();
  });

  alternativeDialog.querySelector('#close-alt-dialog-btn').addEventListener('click', closeDialog);
  backdrop.addEventListener('click', closeDialog);

  function closeDialog() {
    document.body.removeChild(backdrop);
    document.body.removeChild(alternativeDialog);
  }
}

// Lắng nghe message từ popup và background
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'printPage') {
    window.print();
    sendResponse({success: true});
  } else if (request.action === 'printToPDF') {
    // Xử lý print to PDF với tối ưu hóa
    preparePDFPrint().then(() => {
      setTimeout(() => {
        window.print();
        sendResponse({success: true});
      }, 1000);
    });
    return true; // Giữ message channel mở
  } else if (request.action === 'extractPDFData') {
    // Thử extract PDF data từ viewer
    extractPDFFromCurrentPage().then(result => {
      sendResponse(result);
    });
    return true;
  } else if (request.action === 'checkFileAccess') {
    // Kiểm tra quyền truy cập file
    const hasAccess = checkFileAccess();
    sendResponse({hasAccess});
  }
});

// Chuẩn bị trang để in PDF tốt hơn
async function preparePDFPrint() {
  try {
    // Ẩn các element không cần thiết
    const elementsToHide = [
      '.ndfHFb-c4YZDc-haAclf', // Header
      '.a-nEbBXb', // Toolbar
      '.ndfHFb-c4YZDc-LgbsSe', // Sidebar
      '[data-tooltip]', // Tooltips
      '.VIpgJd-TUo6Hb', // Comments
      '.kix-appview-editor-container .docs-texteventtarget-iframe' // Editor overlay
    ];

    elementsToHide.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => {
        el.style.display = 'none';
      });
    });

    // Thêm CSS cho print
    if (!document.querySelector('#print-pdf-style')) {
      const style = document.createElement('style');
      style.id = 'print-pdf-style';
      style.textContent = `
        @media print {
          body * { visibility: hidden; }
          .ndfHFb-c4YZDc-GSWXbd, .ndfHFb-c4YZDc-GSWXbd * {
            visibility: visible;
          }
          .ndfHFb-c4YZDc-GSWXbd {
            position: absolute;
            left: 0;
            top: 0;
            width: 100% !important;
            height: auto !important;
          }
          @page {
            margin: 0.5in;
            size: A4;
          }
        }
      `;
      document.head.appendChild(style);
    }

    // Scroll để load toàn bộ nội dung
    await scrollToLoadContent();

  } catch (error) {
    console.log('Lỗi prepare PDF print:', error);
  }
}

// Scroll để load toàn bộ nội dung
async function scrollToLoadContent() {
  const scrollContainer = document.querySelector('.ndfHFb-c4YZDc-GSWXbd') ||
                         document.querySelector('[role="main"]') ||
                         document.body;

  if (!scrollContainer) return;

  const scrollHeight = scrollContainer.scrollHeight;
  const viewportHeight = window.innerHeight;
  const scrollSteps = Math.ceil(scrollHeight / viewportHeight);

  console.log(`Scrolling ${scrollSteps} steps to load content`);

  for (let i = 0; i < scrollSteps; i++) {
    scrollContainer.scrollTop = i * viewportHeight;
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Scroll về đầu
  scrollContainer.scrollTop = 0;
  await new Promise(resolve => setTimeout(resolve, 500));
}

// Thử extract PDF data từ trang hiện tại
async function extractPDFFromCurrentPage() {
  try {
    // Tìm PDF embed hoặc iframe
    const pdfElements = [
      document.querySelector('embed[type="application/pdf"]'),
      document.querySelector('iframe[src*="pdf"]'),
      document.querySelector('object[type="application/pdf"]'),
      document.querySelector('iframe[src*="drive.google.com"]')
    ].filter(Boolean);

    for (const element of pdfElements) {
      if (element.src) {
        try {
          const response = await fetch(element.src);
          if (response.ok && response.headers.get('content-type')?.includes('pdf')) {
            const arrayBuffer = await response.arrayBuffer();
            return {
              success: true,
              data: Array.from(new Uint8Array(arrayBuffer))
            };
          }
        } catch (error) {
          console.log('Không thể fetch PDF từ element:', error);
        }
      }
    }

    return {success: false, error: 'Không tìm thấy PDF data'};

  } catch (error) {
    console.log('Lỗi extract PDF:', error);
    return {success: false, error: error.message};
  }
}

// Kiểm tra quyền truy cập file
function checkFileAccess() {
  const pageText = document.body.innerText.toLowerCase();
  const restrictedMessages = [
    'sorry, the owner hasn\'t given you permission',
    'only the owner and editors can download',
    'you need permission',
    'access denied',
    'file not found'
  ];

  return !restrictedMessages.some(msg => pageText.includes(msg));
}

// Khởi tạo khi DOM ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

// Khởi tạo lại sau một khoảng thời gian (cho các trang load chậm)
setTimeout(initialize, 2000);
