// Content script để x<PERSON> lý các liên kết file:/// trong Google Sheets
console.log('[FileLinkHandler] Content script loaded. Timestamp:', new Date().toISOString());

class FileLinkHandler {
    constructor() {
        this.isEnabled = true;
        this.processedLinks = new Set();
        this.observer = null;
        this.init();
    }

    async init() {
        // L<PERSON>y cài đặt từ storage
        const result = await chrome.storage.sync.get(['enabled']);
        this.isEnabled = result.enabled !== false;
        
        if (this.isEnabled) {
            console.log('[FileLinkHandler] Enabled. Starting observer and processing existing links.');
            this.startObserving();
            this.processExistingLinks();
        } else {
            console.log('[FileLinkHandler] Disabled.');
        }
        
        // Lắng nghe thay đổi cài đặt
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'sync' && changes.enabled) {
                this.isEnabled = changes.enabled.newValue;
                console.log(`[FileLinkHandler] Enabled state changed to: ${this.isEnabled}`);
                if (this.isEnabled) {
                    this.startObserving();
                    this.processExistingLinks();
                } else {
                    this.stopObserving();
                    this.restoreOriginalLinks();
                }
            }
        });
    }

    startObserving() {
        if (this.observer) {
            console.log('[FileLinkHandler] Observer already active.');
            return;
        }
        console.log('[FileLinkHandler] Starting MutationObserver.');
        this.observer = new MutationObserver((mutations) => {
            // console.log('[FileLinkHandler] MutationObserver detected changes:', mutations);
            let shouldProcess = false;
            for (const mutation of mutations) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldProcess = true;
                    break;
                }
                if (mutation.type === 'characterData') { // Handle direct text changes in cells
                    shouldProcess = true;
                    break;
                }
            }
            
            if (shouldProcess) {
                // console.log('[FileLinkHandler] Scheduling processExistingLinks due to mutations.');
                // Debounce processing to avoid excessive calls
                clearTimeout(this.processTimeout);
                this.processTimeout = setTimeout(() => {
                    console.log('[FileLinkHandler] Debounced: Calling processExistingLinks.');
                    this.processExistingLinks();
                }, 300); // Increased debounce time
            }
        });
        
        this.observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true, // Observe text changes
        });
    }

    stopObserving() {
        if (this.observer) {
            console.log('[FileLinkHandler] Stopping MutationObserver.');
            this.observer.disconnect();
            this.observer = null;
            clearTimeout(this.processTimeout);
        }
    }

    processExistingLinks() {
        console.log('[FileLinkHandler] processExistingLinks called.');
        // Tìm tất cả các cell trong Google Sheets, bao gồm cả editor đang active
        const selectors = [
            '[role="gridcell"]', // General cells
            '.cell-input',      // Cell being edited
            '.formula-bar-input', // Formula bar
            '.kix-lineview',      // Cells in some views (e.g. when a cell is selected)
            '.docs-texteventtarget-iframe' // Check within iframes if Google Sheets uses them for editing
        ];
        const cells = document.querySelectorAll(selectors.join(', '));
        
        console.log(`[FileLinkHandler] Found ${cells.length} potential elements to process.`);
        cells.forEach(cell => this.processCell(cell));

        // Special handling for iframe content if present (Google Sheets might use iframes for cell editors)
        const iframes = document.querySelectorAll('.docs-texteventtarget-iframe');
        iframes.forEach(iframe => {
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                if (iframeDoc) {
                    const iframeCells = iframeDoc.querySelectorAll('body'); // Process the whole body or specific elements
                    iframeCells.forEach(iCell => this.processCell(iCell));
                }
            } catch (e) {
                console.warn('[FileLinkHandler] Error accessing iframe content:', e);
            }
        });
    }

    processCell(cell) {
        if (!cell) return;
        // Using a weak reference or a unique ID for processedLinks might be better for performance with many cells.
        // For now, direct element reference is fine.
        // if (this.processedLinks.has(cell)) return; 

        const text = cell.textContent || cell.innerText || (cell.value || ''); // Also check cell.value for input elements
        if (!text || text.length < 8) return; // Basic check for "file:///"

        // console.log(`[FileLinkHandler] Processing cell. Text content: "${text.substring(0,100)}..."`);
        const fileLinks = this.extractFileLinks(text);
        
        if (fileLinks.length > 0) {
            console.log(`[FileLinkHandler] Found ${fileLinks.length} file links in cell:`, fileLinks, cell);
            // this.processedLinks.add(cell); // Consider if this is still needed or how to manage it effectively
            this.makeLinksClickable(cell, fileLinks);
        }
    }

    extractFileLinks(text) {
        // Regex for file:/// links, designed to be more robust for paths with spaces.
        // Matches file:/// followed by a drive letter (e.g., C:), then any characters
        // that are not generally forbidden in paths, until a common file extension.
        // This regex assumes Windows-style paths primarily but can be adapted.
        const fileLinkRegex = /file:\/\/\/[A-Za-z]:\/[^<>:"|?*\r\n]+\.[A-Za-z0-9_]+/gi;
        let matches = [];
        let match;

        while ((match = fileLinkRegex.exec(text)) !== null) {
            matches.push(match[0]);
        }
        
        // console.log(`[FileLinkHandler] Raw regex matches:`, matches);

        // Lọc và làm sạch các matches
        return matches.map(m => {
            // Loại bỏ các ký tự không hợp lệ ở cuối (ví dụ: dấu chấm câu nếu regex bắt nhầm)
            // For this specific regex, it's less likely to capture trailing punctuation incorrectly.
            return m.trim();
        }).filter(m => {
            // Chỉ giữ lại những đường dẫn hợp lệ
            return m.length > 8 && // Ít nhất "file:///"
                   m.startsWith('file:///') &&
                   !m.includes('..'); // Không chứa đường dẫn relative không an toàn
        });
    }

    makeLinksClickable(cell, fileLinks) {
        if (!fileLinks || fileLinks.length === 0) return;
        console.log(`[FileLinkHandler] makeLinksClickable for cell:`, cell, `with links:`, fileLinks);

        // Check if the cell already contains our links to avoid re-processing
        if (cell.querySelector('.file-link-handler')) {
            // console.log('[FileLinkHandler] Cell already contains processed links. Skipping.');
            // Potentially update existing links if text changed, but for now, skip.
            // A more robust solution would be to diff and update.
            return;
        }

        let contentHTML = cell.innerHTML; // Work with innerHTML to preserve existing HTML structure if any

        // Sắp xếp links theo độ dài giảm dần để tránh thay thế nhầm
        const sortedLinks = [...new Set(fileLinks)].sort((a, b) => b.length - a.length);

        sortedLinks.forEach((link) => {
            const escapedLinkForRegex = this.escapeRegExp(link);
            const regex = new RegExp(escapedLinkForRegex, 'g');
            
            const escapedLinkForAttr = this.escapeHtml(link);
            const escapedTitle = this.escapeHtml(`Nhấp để mở file: ${link}`);
            const clickableLinkHTML = `<a href="#" class="file-link-handler" data-file-path="${escapedLinkForAttr}" title="${escapedTitle}">${this.escapeHtml(link)}</a>`;
            
            // Replace only if not already inside an <a> tag
            // This is a simplified check. A full HTML parser would be more robust.
            if (!contentHTML.includes(`data-file-path="${escapedLinkForAttr}"`)) {
                 contentHTML = contentHTML.replace(regex, clickableLinkHTML);
            }
        });

        if (cell.innerHTML !== contentHTML) {
            console.log(`[FileLinkHandler] Updating cell HTML.`);
            cell.innerHTML = contentHTML;

            // Thêm event listener cho các link mới
            const newLinks = cell.querySelectorAll('.file-link-handler');
            newLinks.forEach(link => {
                // Remove old listener if any, before adding a new one
                // This requires storing the listener or using a unique attribute
                link.removeEventListener('click', this.handleLinkClick); // Assuming handleLinkClick is a bound method
                link.addEventListener('click', this.handleLinkClick.bind(this));
            });
        } else {
            // console.log(`[FileLinkHandler] No change in HTML content for cell.`);
        }
    }

    handleLinkClick(e) {
        e.preventDefault();
        e.stopPropagation();
        const linkElement = e.currentTarget;
        const filePath = linkElement.getAttribute('data-file-path');
        console.log(`[FileLinkHandler] Link clicked: ${filePath}`);
        this.openFile(filePath);
    }
    
    escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    async openFile(filePath) {
        try {
            // Gửi message đến background script để mở file
            await chrome.runtime.sendMessage({
                action: 'openFile',
                filePath: filePath
            });
            
            // Hiển thị thông báo thành công
            this.showNotification(`Đang mở file: ${filePath}`, 'success');
        } catch (error) {
            console.error('Lỗi khi mở file:', error);
            this.showNotification(`Không thể mở file: ${filePath}`, 'error');
        }
    }

    showNotification(message, type = 'info') {
        // Tạo notification đơn giản
        const notification = document.createElement('div');
        notification.className = `file-link-notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#f44336' : '#4CAF50'};
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    restoreOriginalLinks() {
        const processedLinks = document.querySelectorAll('.file-link-handler');
        processedLinks.forEach(link => {
            const filePath = link.getAttribute('data-file-path');
            link.outerHTML = filePath;
        });
        this.processedLinks.clear();
    }
}

// Khởi tạo handler khi trang được load
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new FileLinkHandler();
    });
} else {
    new FileLinkHandler();
}

// Lắng nghe messages từ popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'toggleEnabled') {
        chrome.storage.sync.set({ enabled: request.enabled });
        sendResponse({ success: true });
    }
});
