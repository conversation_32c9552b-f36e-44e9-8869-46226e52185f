import os
import sys
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from datetime import datetime
import threading
import time

class LogViewer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Debug Log Viewer - Dự án đọc tài liệu")
        self.root.geometry("1000x700")
        
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.log_files = {
            'rename_pdf_debug.log': 'Rename PDF Debug',
            'debug.log': 'Main App Debug',
            'config_debug.log': 'Config Debug'
        }
        
        self.current_log_file = None
        self.auto_refresh = False
        self.refresh_thread = None
        
        self.setup_ui()
        self.load_available_logs()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Header frame
        header_frame = ttk.Frame(main_frame)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        header_frame.columnconfigure(2, weight=1)
        
        # Log file selection
        ttk.Label(header_frame, text="Log file:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        self.log_var = tk.StringVar()
        self.log_combo = ttk.Combobox(header_frame, textvariable=self.log_var, 
                                     state="readonly", width=30)
        self.log_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        self.log_combo.bind('<<ComboboxSelected>>', self.on_log_selected)
        
        # Buttons
        button_frame = ttk.Frame(header_frame)
        button_frame.grid(row=0, column=2, sticky=tk.E)
        
        ttk.Button(button_frame, text="Refresh", command=self.refresh_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Clear Log", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Open Folder", command=self.open_log_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Save As...", command=self.save_log_as).pack(side=tk.LEFT)
        
        # Auto refresh
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.auto_refresh_var = tk.BooleanVar()
        auto_refresh_cb = ttk.Checkbutton(control_frame, text="Auto refresh (5s)", 
                                         variable=self.auto_refresh_var,
                                         command=self.toggle_auto_refresh)
        auto_refresh_cb.pack(side=tk.LEFT)
        
        # Filter frame
        filter_frame = ttk.Frame(control_frame)
        filter_frame.pack(side=tk.RIGHT)
        
        ttk.Label(filter_frame, text="Filter:").pack(side=tk.LEFT, padx=(0, 5))
        self.filter_var = tk.StringVar()
        self.filter_entry = ttk.Entry(filter_frame, textvariable=self.filter_var, width=20)
        self.filter_entry.pack(side=tk.LEFT, padx=(0, 5))
        self.filter_entry.bind('<KeyRelease>', self.apply_filter)
        
        ttk.Button(filter_frame, text="Clear", command=self.clear_filter).pack(side=tk.LEFT)
        
        # Log content
        log_frame = ttk.LabelFrame(main_frame, text="Log Content", padding="5")
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, 
                                                 font=("Consolas", 9))
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure text tags for different log levels
        self.log_text.tag_configure("ERROR", foreground="red", font=("Consolas", 9, "bold"))
        self.log_text.tag_configure("WARNING", foreground="orange", font=("Consolas", 9, "bold"))
        self.log_text.tag_configure("INFO", foreground="blue")
        self.log_text.tag_configure("DEBUG", foreground="gray")
        self.log_text.tag_configure("TIMESTAMP", foreground="green")
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def load_available_logs(self):
        """Load danh sách log files có sẵn"""
        available_logs = []
        
        for log_file, description in self.log_files.items():
            log_path = os.path.join(self.script_dir, log_file)
            if os.path.exists(log_path):
                file_size = os.path.getsize(log_path)
                mod_time = datetime.fromtimestamp(os.path.getmtime(log_path))
                display_name = f"{description} ({file_size:,} bytes, {mod_time.strftime('%H:%M:%S')})"
                available_logs.append((display_name, log_path))
        
        if available_logs:
            self.log_combo['values'] = [item[0] for item in available_logs]
            self.log_paths = {item[0]: item[1] for item in available_logs}
            self.log_combo.current(0)
            self.on_log_selected()
        else:
            self.status_var.set("Không tìm thấy log files")
    
    def on_log_selected(self, event=None):
        """Khi chọn log file"""
        selected = self.log_var.get()
        if selected and selected in self.log_paths:
            self.current_log_file = self.log_paths[selected]
            self.refresh_log()
    
    def refresh_log(self):
        """Refresh nội dung log"""
        if not self.current_log_file or not os.path.exists(self.current_log_file):
            self.log_text.delete(1.0, tk.END)
            self.log_text.insert(tk.END, "Log file không tồn tại")
            return
        
        try:
            with open(self.current_log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.log_text.delete(1.0, tk.END)
            
            # Parse và highlight log content
            lines = content.split('\n')
            for line in lines:
                if not line.strip():
                    self.log_text.insert(tk.END, '\n')
                    continue
                
                # Detect log level and apply formatting
                if ' - ERROR - ' in line:
                    self.insert_formatted_line(line, "ERROR")
                elif ' - WARNING - ' in line:
                    self.insert_formatted_line(line, "WARNING")
                elif ' - INFO - ' in line:
                    self.insert_formatted_line(line, "INFO")
                elif ' - DEBUG - ' in line:
                    self.insert_formatted_line(line, "DEBUG")
                else:
                    self.log_text.insert(tk.END, line + '\n')
            
            # Scroll to bottom
            self.log_text.see(tk.END)
            
            # Update status
            file_size = os.path.getsize(self.current_log_file)
            line_count = len(lines)
            self.status_var.set(f"Loaded {line_count:,} lines, {file_size:,} bytes")
            
        except Exception as e:
            messagebox.showerror("Error", f"Không thể đọc log file: {e}")
    
    def insert_formatted_line(self, line, level):
        """Insert một dòng với formatting"""
        # Extract timestamp if present
        if ' - ' in line:
            parts = line.split(' - ', 2)
            if len(parts) >= 3:
                timestamp = parts[0]
                log_level = parts[1]
                message = parts[2]
                
                # Insert timestamp
                self.log_text.insert(tk.END, timestamp, "TIMESTAMP")
                self.log_text.insert(tk.END, " - ")
                
                # Insert log level
                self.log_text.insert(tk.END, log_level, level)
                self.log_text.insert(tk.END, " - ")
                
                # Insert message
                self.log_text.insert(tk.END, message + '\n')
            else:
                self.log_text.insert(tk.END, line + '\n', level)
        else:
            self.log_text.insert(tk.END, line + '\n', level)
    
    def apply_filter(self, event=None):
        """Áp dụng filter"""
        filter_text = self.filter_var.get().lower()
        if not filter_text:
            self.refresh_log()
            return
        
        if not self.current_log_file or not os.path.exists(self.current_log_file):
            return
        
        try:
            with open(self.current_log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.log_text.delete(1.0, tk.END)
            
            # Filter lines
            lines = content.split('\n')
            filtered_lines = [line for line in lines if filter_text in line.lower()]
            
            for line in filtered_lines:
                if ' - ERROR - ' in line:
                    self.insert_formatted_line(line, "ERROR")
                elif ' - WARNING - ' in line:
                    self.insert_formatted_line(line, "WARNING")
                elif ' - INFO - ' in line:
                    self.insert_formatted_line(line, "INFO")
                elif ' - DEBUG - ' in line:
                    self.insert_formatted_line(line, "DEBUG")
                else:
                    self.log_text.insert(tk.END, line + '\n')
            
            self.status_var.set(f"Filtered: {len(filtered_lines):,} lines")
            
        except Exception as e:
            messagebox.showerror("Error", f"Không thể filter log: {e}")
    
    def clear_filter(self):
        """Clear filter"""
        self.filter_var.set("")
        self.refresh_log()
    
    def clear_log(self):
        """Clear log file"""
        if not self.current_log_file:
            return
        
        if messagebox.askyesno("Confirm", "Bạn có chắc muốn xóa log file?"):
            try:
                with open(self.current_log_file, 'w', encoding='utf-8') as f:
                    f.write("")
                self.refresh_log()
                self.status_var.set("Log cleared")
            except Exception as e:
                messagebox.showerror("Error", f"Không thể xóa log: {e}")
    
    def open_log_folder(self):
        """Mở thư mục chứa log files"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.script_dir)
            else:  # Linux/Mac
                import subprocess
                subprocess.Popen(['xdg-open', self.script_dir])
        except Exception as e:
            messagebox.showerror("Error", f"Không thể mở thư mục: {e}")
    
    def save_log_as(self):
        """Save log content to file"""
        if not self.current_log_file:
            return
        
        file_path = filedialog.asksaveasfilename(
            title="Save log as...",
            defaultextension=".log",
            filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                content = self.log_text.get(1.0, tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.status_var.set(f"Saved to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Không thể lưu file: {e}")
    
    def toggle_auto_refresh(self):
        """Toggle auto refresh"""
        self.auto_refresh = self.auto_refresh_var.get()
        
        if self.auto_refresh:
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()
    
    def start_auto_refresh(self):
        """Start auto refresh thread"""
        if self.refresh_thread and self.refresh_thread.is_alive():
            return
        
        self.refresh_thread = threading.Thread(target=self.auto_refresh_worker, daemon=True)
        self.refresh_thread.start()
    
    def stop_auto_refresh(self):
        """Stop auto refresh"""
        self.auto_refresh = False
    
    def auto_refresh_worker(self):
        """Auto refresh worker thread"""
        while self.auto_refresh:
            try:
                self.root.after(0, self.refresh_log)
                time.sleep(5)
            except:
                break
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = LogViewer()
    app.run()
