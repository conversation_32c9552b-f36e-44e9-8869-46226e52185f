# Google Drive PDF Downloader

Tiện ích Chrome giúp tải xuống file PDF từ Google Drive ngay cả khi chỉ có quyền xem.

## Tính năng

- ✅ **Tải xuống PDF** từ Google Drive chỉ với quyền xem
- ✅ **Phương pháp lách thông minh** đ<PERSON> bypass hạn chế tải xuống
- ✅ **5+ phương pháp bypass** khác nhau (Google Cache, Web Archive, Proxy, v.v.)
- ✅ **Screenshot to PDF** khi không thể tải trực tiếp
- ✅ **Print to PDF** với tối ưu hóa layout
- ✅ **Tự động phát hiện** file PDF trên trang Google Drive
- ✅ **Nút tải xuống** tiện lợi ngay trên trang
- ✅ **Popup thông minh** với nhiều tùy chọn bypass
- ✅ **Sao chép link** tải xuống trực tiếp
- ✅ **Giao diện đẹp** theo Material Design

## Cài đặt

### Cách 1: Cài đặt từ Chrome Web Store (Sắp có)
1. Truy cập Chrome Web Store
2. Tìm kiếm "Google Drive PDF Downloader"
3. Nhấp "Add to Chrome"

### Cách 2: Cài đặt thủ công (Developer Mode)
1. Tải xuống hoặc clone repository này
2. Mở Chrome và truy cập `chrome://extensions/`
3. Bật "Developer mode" ở góc trên bên phải
4. Nhấp "Load unpacked" và chọn thư mục `google-drive-pdf-downloader`
5. Extension sẽ xuất hiện trong danh sách extensions

## Cách sử dụng

### Phương pháp 1: Sử dụng nút trên trang
1. Mở file PDF trên Google Drive (URL dạng: `https://drive.google.com/file/d/FILE_ID/view`)
2. Nút "Tải PDF" sẽ tự động xuất hiện ở góc trên bên phải
3. Nhấp vào nút để tải xuống file

### Phương pháp 2: Sử dụng popup
1. Mở file PDF trên Google Drive
2. Nhấp vào icon extension trên thanh công cụ Chrome
3. Trong popup, nhấp "Tải xuống PDF" hoặc "Sao chép link tải"

## Cách hoạt động

Extension hoạt động bằng cách:
1. Phát hiện URL Google Drive có dạng `/file/d/FILE_ID/view`
2. Trích xuất FILE_ID từ URL
3. Chuyển đổi thành URL tải xuống: `https://drive.google.com/uc?export=download&id=FILE_ID`
4. Sử dụng Chrome Downloads API để tải file

## Hỗ trợ

### URL được hỗ trợ
- `https://drive.google.com/file/d/*/view*`
- `https://docs.google.com/document/d/*/edit*` (cho Google Docs)

### Yêu cầu quyền
- `activeTab`: Truy cập tab hiện tại
- `storage`: Lưu trữ cài đặt
- `downloads`: Tải xuống file
- `https://drive.google.com/*`: Truy cập Google Drive
- `https://docs.google.com/*`: Truy cập Google Docs

## Hạn chế và Giải pháp

### ⚠️ File bị hạn chế tải xuống
Một số file trên Google Drive có thể bị chủ sở hữu hạn chế quyền tải xuống. Khi gặp thông báo:
> "Sorry, the owner hasn't given you permission to download this file"

**Giải pháp thay thế:**
1. **In thành PDF** (Khuyến nghị):
   - Nhấp nút "In thành PDF" trong extension
   - Hoặc nhấn `Ctrl+P` → Chọn "Save as PDF"

2. **Sao chép link tải xuống**:
   - Extension sẽ tạo link tải xuống trực tiếp
   - Thử mở link trong tab ẩn danh

3. **Mở tab mới**:
   - Extension sẽ mở tab mới với URL tải xuống
   - Có thể cần đăng nhập lại

4. **Chụp ảnh màn hình**:
   - Sử dụng công cụ chụp màn hình
   - Hoặc extension chụp màn hình khác

## Khắc phục sự cố

### File không tải được
1. **Kiểm tra quyền truy cập**: Đảm bảo bạn có quyền xem file
2. **Thử phương pháp khác**: Sử dụng "In thành PDF" nếu tải xuống trực tiếp thất bại
3. **Kiểm tra định dạng**: Đảm bảo file là PDF hoặc có thể chuyển đổi
4. **Tải lại trang**: Refresh và thử lại
5. **Kiểm tra popup**: Xem thông báo lỗi chi tiết

### Nút không xuất hiện
1. **URL đúng định dạng**: Đảm bảo URL có dạng `/file/d/FILE_ID/view`
2. **Tải lại trang**: Refresh trang Google Drive
3. **Extension đã bật**: Kiểm tra trong `chrome://extensions/`
4. **Đợi trang load**: Một số trang load chậm, đợi vài giây

### Lỗi quyền truy cập
1. **Đăng nhập lại**: Logout và login lại Google Drive
2. **Tab ẩn danh**: Thử mở file trong tab ẩn danh
3. **Liên hệ chủ sở hữu**: Yêu cầu quyền tải xuống
4. **Sử dụng "In thành PDF"**: Phương pháp thay thế hiệu quả nhất

### Lỗi khác
1. **Developer Tools**: Mở F12 → Console để xem lỗi
2. **Reload extension**: Tắt/bật lại extension
3. **Restart Chrome**: Khởi động lại trình duyệt
4. **Báo cáo lỗi**: Gửi thông tin chi tiết về lỗi

## Phát triển

### Cấu trúc project
```
google-drive-pdf-downloader/
├── manifest.json          # Cấu hình extension
├── background.js          # Service worker
├── content.js            # Script chạy trên trang
├── popup.html            # Giao diện popup
├── popup.js              # Logic popup
├── popup.css             # Styling popup
├── icons/                # Icons extension
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md             # Tài liệu này
```

### Chạy trong môi trường phát triển
1. Clone repository
2. Mở Chrome Extensions (`chrome://extensions/`)
3. Bật Developer mode
4. Load unpacked extension
5. Thực hiện thay đổi và reload extension

## Giấy phép

MIT License - Xem file LICENSE để biết thêm chi tiết.

## Đóng góp

Mọi đóng góp đều được chào đón! Vui lòng:
1. Fork repository
2. Tạo feature branch
3. Commit thay đổi
4. Push và tạo Pull Request

## Changelog

### v1.0.0 (2024-01-XX)
- Phiên bản đầu tiên
- Tính năng tải PDF từ Google Drive
- Giao diện popup
- Nút tải xuống trên trang
