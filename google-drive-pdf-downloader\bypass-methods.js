// <PERSON><PERSON><PERSON> phương pháp lách nâng cao để tải PDF từ Google Drive

// Phương pháp 1: <PERSON><PERSON> dụng Google Apps Script API
async function tryAppsScriptBypass(fileId, fileName) {
  console.log('Thử phương pháp Apps Script bypass...');

  try {
    // Tạo Apps Script để download file
    const scriptCode = `
      function downloadFile() {
        var fileId = '${fileId}';
        var file = DriveApp.getFileById(fileId);
        var blob = file.getBlob();
        return Utilities.base64Encode(blob.getBytes());
      }
    `;

    // Tạo project Apps Script tạm thời
    const projectResponse = await fetch('https://script.googleapis.com/v1/projects', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + await getAccessToken(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: 'PDF Downloader Temp',
        parentId: 'root'
      })
    });

    if (projectResponse.ok) {
      const project = await projectResponse.json();
      // Deploy và execute script
      // ... implementation
      return true;
    }

  } catch (error) {
    console.log('Apps Script bypass thất bại:', error);
  }

  return false;
}

// Phương pháp 2: Sử dụng Google Drive API với different scope
async function tryDriveAPIBypass(fileId, fileName) {
  console.log('Thử phương pháp Drive API bypass...');

  const apiUrls = [
    `https://www.googleapis.com/drive/v3/files/${fileId}?alt=media`,
    `https://www.googleapis.com/drive/v2/files/${fileId}?alt=media`,
    `https://www.googleapis.com/drive/v3/files/${fileId}/export?mimeType=application/pdf`,
    `https://content.googleapis.com/drive/v3/files/${fileId}?alt=media`,
  ];

  for (const url of apiUrls) {
    try {
      const response = await fetch(url, {
        headers: {
          'Authorization': 'Bearer ' + await getAccessToken()
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        if (blob.size > 1000) { // Đảm bảo không phải error message
          const downloadUrl = URL.createObjectURL(blob);

          await chrome.downloads.download({
            url: downloadUrl,
            filename: fileName,
            saveAs: true
          });

          return true;
        }
      }
    } catch (error) {
      console.log('API URL thất bại:', url, error);
    }
  }

  return false;
}

// Phương pháp 3: Sử dụng iframe sandbox để bypass
async function tryIframeSandboxBypass(fileId, fileName) {
  console.log('Thử phương pháp iframe sandbox bypass...');

  return new Promise((resolve) => {
    // Tạo iframe sandbox
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.sandbox = 'allow-scripts allow-same-origin';
    iframe.src = `https://drive.google.com/file/d/${fileId}/preview`;

    iframe.onload = () => {
      try {
        // Inject script vào iframe
        const script = iframe.contentDocument.createElement('script');
        script.textContent = `
          // Tìm PDF data trong iframe
          const pdfElements = document.querySelectorAll('embed, object, iframe');
          for (const element of pdfElements) {
            if (element.src && element.src.includes('blob:')) {
              fetch(element.src)
                .then(response => response.blob())
                .then(blob => {
                  const url = URL.createObjectURL(blob);
                  parent.postMessage({
                    type: 'PDF_FOUND',
                    url: url,
                    fileName: '${fileName}'
                  }, '*');
                });
              break;
            }
          }
        `;

        iframe.contentDocument.head.appendChild(script);

        // Timeout sau 10 giây
        setTimeout(() => {
          document.body.removeChild(iframe);
          resolve(false);
        }, 10000);

      } catch (error) {
        console.log('Iframe script injection thất bại:', error);
        document.body.removeChild(iframe);
        resolve(false);
      }
    };

    // Listen for message từ iframe
    window.addEventListener('message', (event) => {
      if (event.data.type === 'PDF_FOUND') {
        chrome.downloads.download({
          url: event.data.url,
          filename: event.data.fileName,
          saveAs: true
        });

        document.body.removeChild(iframe);
        resolve(true);
      }
    });

    document.body.appendChild(iframe);
  });
}

// Phương pháp 4: Sử dụng Service Worker để intercept requests
async function tryServiceWorkerBypass(fileId, fileName) {
  console.log('Thử phương pháp Service Worker bypass...');

  try {
    // Register service worker
    const registration = await navigator.serviceWorker.register('/sw-bypass.js');

    // Gửi message đến service worker
    registration.active.postMessage({
      action: 'INTERCEPT_PDF',
      fileId: fileId,
      fileName: fileName
    });

    return new Promise((resolve) => {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data.action === 'PDF_INTERCEPTED') {
          resolve(true);
        } else if (event.data.action === 'INTERCEPT_FAILED') {
          resolve(false);
        }
      });

      // Timeout
      setTimeout(() => resolve(false), 15000);
    });

  } catch (error) {
    console.log('Service Worker bypass thất bại:', error);
    return false;
  }
}

// Phương pháp 5: Sử dụng WebRTC để bypass (experimental)
async function tryWebRTCBypass(fileId, fileName) {
  console.log('Thử phương pháp WebRTC bypass...');

  try {
    // Tạo RTCPeerConnection để tunnel data
    const pc = new RTCPeerConnection();
    const dc = pc.createDataChannel('pdf-transfer');

    dc.onopen = () => {
      // Request PDF data qua data channel
      dc.send(JSON.stringify({
        action: 'REQUEST_PDF',
        fileId: fileId
      }));
    };

    dc.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'PDF_DATA') {
          const blob = new Blob([new Uint8Array(data.data)], {type: 'application/pdf'});
          const url = URL.createObjectURL(blob);

          chrome.downloads.download({
            url: url,
            filename: fileName,
            saveAs: true
          });
        }
      } catch (error) {
        console.log('WebRTC data parse error:', error);
      }
    };

    // Create offer và setup connection
    const offer = await pc.createOffer();
    await pc.setLocalDescription(offer);

    // Timeout
    setTimeout(() => pc.close(), 10000);

    return true;

  } catch (error) {
    console.log('WebRTC bypass thất bại:', error);
    return false;
  }
}

// Helper function để lấy access token
async function getAccessToken() {
  try {
    // Thử lấy token từ Google APIs
    const response = await fetch('https://accounts.google.com/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        'grant_type': 'authorization_code',
        'scope': 'https://www.googleapis.com/auth/drive.readonly'
      })
    });

    if (response.ok) {
      const data = await response.json();
      return data.access_token;
    }
  } catch (error) {
    console.log('Không thể lấy access token:', error);
  }

  return null;
}

// Export functions
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    tryAppsScriptBypass,
    tryDriveAPIBypass,
    tryIframeSandboxBypass,
    tryServiceWorkerBypass,
    tryWebRTCBypass
  };
}