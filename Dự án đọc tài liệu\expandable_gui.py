#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Expandable GUI cho PDF Context Menu Manager
"""

import tkinter as tk
from tkinter import messagebox, filedialog, simpledialog
from tkinter import ttk
import os
import sys

# Import manager
try:
    from register_pdf_context_menu import PDFContextMenuManager
    print("✅ Import thành công")
except Exception as e:
    print(f"❌ Lỗi import: {e}")
    sys.exit(1)

class ExpandableGUI:
    def __init__(self):
        self.manager = PDFContextMenuManager()
        
        self.root = tk.Tk()
        self.root.title("🔧 PDF Context Menu Manager - Expandable")
        
        # Tính toán kích thước màn hình
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # Kích thước ban đầu
        self.normal_width = 900
        self.normal_height = 700
        self.expanded_width = min(1200, int(screen_width * 0.9))
        self.expanded_height = min(900, int(screen_height * 0.9))
        
        # Đặt vị trí giữa màn hình
        x = (screen_width - self.normal_width) // 2
        y = (screen_height - self.normal_height) // 2
        
        self.root.geometry(f"{self.normal_width}x{self.normal_height}+{x}+{y}")
        self.root.resizable(True, True)
        self.root.minsize(800, 600)
        
        self.is_expanded = False
        self.setup_ui()
        
    def setup_ui(self):
        # Main container với notebook để tổ chức tốt hơn
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab 1: Cấu hình chính
        self.config_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.config_tab, text="⚙️ Cấu hình")
        
        # Tab 2: Điều khiển
        self.control_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.control_tab, text="🎛️ Điều khiển")
        
        # Tab 3: Hướng dẫn
        self.help_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.help_tab, text="📖 Hướng dẫn")
        
        self.setup_config_tab()
        self.setup_control_tab()
        self.setup_help_tab()
        
    def setup_config_tab(self):
        # Scrollable frame cho config tab
        canvas = tk.Canvas(self.config_tab)
        scrollbar = ttk.Scrollbar(self.config_tab, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Mouse wheel scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind("<MouseWheel>", _on_mousewheel)
        
        main_frame = ttk.Frame(scrollable_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title và expand button
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="🔧 Quản lý Context Menu cho PDF", 
                 font=("Arial", 16, "bold")).pack(side=tk.LEFT)
        
        self.expand_btn = ttk.Button(title_frame, text="📏 Mở rộng", 
                                    command=self.toggle_expand)
        self.expand_btn.pack(side=tk.RIGHT)
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="📊 Trạng thái", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Admin status
        admin_status = "✅ Có quyền Administrator" if self.manager.is_admin() else "❌ Không có quyền Administrator"
        admin_color = "green" if self.manager.is_admin() else "red"
        ttk.Label(status_frame, text=f"🔐 {admin_status}", foreground=admin_color).pack(anchor=tk.W)
        
        # Registration status
        self.reg_status_label = ttk.Label(status_frame, text="")
        self.reg_status_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Changes status
        self.changes_status_label = ttk.Label(status_frame, text="")
        self.changes_status_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Config frame
        config_frame = ttk.LabelFrame(main_frame, text="⚙️ Cấu hình", padding="15")
        config_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Python path
        ttk.Label(config_frame, text="🐍 Đường dẫn Python:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        python_frame = ttk.Frame(config_frame)
        python_frame.pack(fill=tk.X, pady=(5, 15))
        
        self.python_var = tk.StringVar(value=self.manager.config.get('python_path', ''))
        self.python_var.trace_add('write', self.on_config_change)
        python_entry = ttk.Entry(python_frame, textvariable=self.python_var, font=("Consolas", 9))
        python_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(python_frame, text="📁", command=self.browse_python).pack(side=tk.RIGHT, padx=(5, 0))
        
        # Script path
        ttk.Label(config_frame, text="📄 Đường dẫn Script:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        script_frame = ttk.Frame(config_frame)
        script_frame.pack(fill=tk.X, pady=(5, 15))
        
        self.script_var = tk.StringVar(value=self.manager.config.get('rename_script_path', ''))
        self.script_var.trace_add('write', self.on_config_change)
        script_entry = ttk.Entry(script_frame, textvariable=self.script_var, font=("Consolas", 9))
        script_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(script_frame, text="📁", command=self.browse_script).pack(side=tk.RIGHT, padx=(5, 0))
        
        # Menu text
        ttk.Label(config_frame, text="📝 Tên hiển thị trong menu:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        self.menu_var = tk.StringVar(value=self.manager.config.get('menu_text', ''))
        self.menu_var.trace_add('write', self.on_config_change)
        ttk.Entry(config_frame, textvariable=self.menu_var).pack(fill=tk.X, pady=(5, 15))
        
        # Icon path
        ttk.Label(config_frame, text="🎨 Đường dẫn Icon (tùy chọn):", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        icon_frame = ttk.Frame(config_frame)
        icon_frame.pack(fill=tk.X, pady=(5, 15))
        
        self.icon_var = tk.StringVar(value=self.manager.config.get('icon_path', ''))
        self.icon_var.trace_add('write', self.on_config_change)
        icon_entry = ttk.Entry(icon_frame, textvariable=self.icon_var, font=("Consolas", 9))
        icon_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(icon_frame, text="📁", command=self.browse_icon).pack(side=tk.RIGHT, padx=(5, 0))
        
        # VBS option
        self.vbs_var = tk.BooleanVar(value=self.manager.config.get('use_vbs', True))
        self.vbs_var.trace_add('write', self.on_config_change)
        ttk.Checkbutton(config_frame, text="🔇 Ẩn cửa sổ terminal khi chạy", 
                       variable=self.vbs_var).pack(anchor=tk.W)
        
        # Update status
        self.update_status()
        
    def setup_control_tab(self):
        main_frame = ttk.Frame(self.control_tab, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Save section
        save_frame = ttk.LabelFrame(main_frame, text="💾 Lưu cấu hình", padding="15")
        save_frame.pack(fill=tk.X, pady=(0, 20))
        
        save_buttons = ttk.Frame(save_frame)
        save_buttons.pack(fill=tk.X)
        
        ttk.Button(save_buttons, text="💾 Lưu cấu hình", 
                  command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(save_buttons, text="✨ Lưu và chuẩn hóa", 
                  command=self.save_and_normalize).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(save_buttons, text="🔄 Tự động lưu", 
                  command=self.auto_save).pack(side=tk.LEFT)
        
        # Test section
        test_frame = ttk.LabelFrame(main_frame, text="🧪 Kiểm tra", padding="15")
        test_frame.pack(fill=tk.X, pady=(0, 20))
        
        test_buttons = ttk.Frame(test_frame)
        test_buttons.pack(fill=tk.X)
        
        ttk.Button(test_buttons, text="🔍 Kiểm tra trạng thái", 
                  command=self.check_status).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(test_buttons, text="🔧 Test chuẩn hóa đường dẫn", 
                  command=self.test_normalize).pack(side=tk.LEFT)
        
        # Admin section
        admin_frame = ttk.LabelFrame(main_frame, text="🔐 Quản lý Context Menu (cần Admin)", padding="15")
        admin_frame.pack(fill=tk.X)
        
        admin_buttons = ttk.Frame(admin_frame)
        admin_buttons.pack(fill=tk.X)
        
        ttk.Button(admin_buttons, text="✅ Đăng ký Context Menu", 
                  command=self.register_admin).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(admin_buttons, text="❌ Hủy đăng ký Context Menu", 
                  command=self.unregister_admin).pack(side=tk.LEFT)
        
    def setup_help_tab(self):
        main_frame = ttk.Frame(self.help_tab, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Scrollable text
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Arial", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        help_content = """🚀 TÍNH NĂNG MỚI:

✨ Chuẩn hóa đường dẫn tự động
   • Chuyển đổi G:/path thành G:\\\\path
   • Áp dụng cho tất cả đường dẫn khi lưu

🔐 Quản lý quyền Administrator tự động
   • Tự động phát hiện quyền admin
   • Tự động yêu cầu quyền khi cần

📝 Theo dõi thay đổi real-time
   • Hiển thị trạng thái thay đổi
   • Cảnh báo khi có thay đổi chưa lưu

🎛️ Giao diện mở rộng
   • Có thể mở rộng/thu nhỏ cửa sổ
   • Scrollbar cho nội dung dài
   • Tab để tổ chức tốt hơn

📋 CÁCH SỬ DỤNG:

1️⃣ Cấu hình đường dẫn:
   • Nhập đường dẫn Python và script
   • Sử dụng nút 📁 để browse file
   • Đường dẫn sẽ tự động chuẩn hóa

2️⃣ Lưu cấu hình:
   • "💾 Lưu cấu hình": Lưu cơ bản
   • "✨ Lưu và chuẩn hóa": Lưu + chuẩn hóa đường dẫn
   • "🔄 Tự động lưu": Lưu nếu có thay đổi

3️⃣ Kiểm tra:
   • "🔍 Kiểm tra trạng thái": Xem trạng thái đăng ký
   • "🔧 Test chuẩn hóa": Test chức năng chuẩn hóa

4️⃣ Đăng ký Context Menu:
   • "✅ Đăng ký": Tự động yêu cầu quyền admin
   • "❌ Hủy đăng ký": Xóa context menu

⚠️ LƯU Ý QUAN TRỌNG:

🔹 Đường dẫn sẽ được chuẩn hóa thành:
   G:\\\\My Drive\\\\folder\\\\file.py

🔹 Cần quyền Administrator để đăng ký/hủy context menu

🔹 Có thể kéo thay đổi kích thước cửa sổ

🔹 Sử dụng nút "📏 Mở rộng" để mở rộng/thu nhỏ

🔹 Tất cả thay đổi được theo dõi real-time"""
        
        text_widget.insert(tk.END, help_content)
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def toggle_expand(self):
        """Chuyển đổi kích thước cửa sổ"""
        if self.is_expanded:
            self.root.geometry(f"{self.normal_width}x{self.normal_height}")
            self.expand_btn.config(text="📏 Mở rộng")
            self.is_expanded = False
        else:
            self.root.geometry(f"{self.expanded_width}x{self.expanded_height}")
            self.expand_btn.config(text="📐 Thu nhỏ")
            self.is_expanded = True
    
    def on_config_change(self, *args):
        """Callback khi có thay đổi"""
        try:
            self.manager.update_config('python_path', self.python_var.get())
            self.manager.update_config('rename_script_path', self.script_var.get())
            self.manager.update_config('menu_text', self.menu_var.get())
            self.manager.update_config('icon_path', self.icon_var.get())
            self.manager.update_config('use_vbs', self.vbs_var.get())
            self.update_status()
        except Exception as e:
            print(f"Error in config change: {e}")
    
    def update_status(self):
        """Cập nhật trạng thái"""
        # Registration status
        if hasattr(self.manager, 'is_registered'):
            if self.manager.is_registered():
                self.reg_status_label.config(text="📋 ✅ Context menu đã được đăng ký", foreground="green")
            else:
                self.reg_status_label.config(text="📋 ❌ Context menu chưa được đăng ký", foreground="red")
        
        # Changes status
        if hasattr(self.manager, 'has_unsaved_changes'):
            if self.manager.has_unsaved_changes():
                self.changes_status_label.config(text="📝 ⚠️ Có thay đổi chưa lưu", foreground="orange")
            else:
                self.changes_status_label.config(text="📝 ✅ Tất cả thay đổi đã lưu", foreground="green")
    
    def browse_python(self):
        file_path = filedialog.askopenfilename(
            title="Chọn Python executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if file_path:
            normalized = self.manager.normalize_path(file_path)
            self.python_var.set(normalized)
    
    def browse_script(self):
        file_path = filedialog.askopenfilename(
            title="Chọn rename_pdf.py",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")]
        )
        if file_path:
            normalized = self.manager.normalize_path(file_path)
            self.script_var.set(normalized)
    
    def browse_icon(self):
        file_path = filedialog.askopenfilename(
            title="Chọn file icon",
            filetypes=[("Icon files", "*.ico"), ("Image files", "*.png;*.jpg;*.bmp"), ("All files", "*.*")]
        )
        if file_path:
            normalized = self.manager.normalize_path(file_path)
            self.icon_var.set(normalized)
    
    def save_config(self):
        self.manager.save_config()
        self.update_status()
        messagebox.showinfo("Thành công", "Đã lưu cấu hình!")
    
    def save_and_normalize(self):
        self.manager.save_config()
        # Update GUI với đường dẫn đã chuẩn hóa
        self.python_var.set(self.manager.config['python_path'])
        self.script_var.set(self.manager.config['rename_script_path'])
        self.icon_var.set(self.manager.config['icon_path'])
        self.update_status()
        
        paths_info = f"""Đã lưu và chuẩn hóa đường dẫn:

🐍 Python: {self.manager.config['python_path']}

📄 Script: {self.manager.config['rename_script_path']}

🎨 Icon: {self.manager.config['icon_path'] or '(không có)'}

✅ Tất cả đường dẫn đã sử dụng \\\\ như yêu cầu!"""
        
        messagebox.showinfo("Thành công", paths_info)
    
    def auto_save(self):
        if self.manager.auto_save_if_changed():
            self.update_status()
            messagebox.showinfo("Tự động lưu", "Đã lưu các thay đổi!")
        else:
            messagebox.showinfo("Tự động lưu", "Không có thay đổi nào cần lưu.")
    
    def check_status(self):
        self.update_status()
        messagebox.showinfo("Trạng thái", "Đã cập nhật trạng thái!")
    
    def test_normalize(self):
        test_path = simpledialog.askstring(
            "Test chuẩn hóa",
            "Nhập đường dẫn để test:",
            initialvalue="G:/My Drive/test/file.py"
        )
        if test_path:
            normalized = self.manager.normalize_path(test_path)
            messagebox.showinfo("Kết quả", f"Input: {test_path}\\n\\nOutput: {normalized}")
    
    def register_admin(self):
        self.save_and_normalize()
        success, message = self.manager.register_context_menu(force_admin=True)
        if success:
            messagebox.showinfo("Thành công", f"✅ {message}")
        else:
            messagebox.showerror("Lỗi", f"❌ {message}")
        self.update_status()
    
    def unregister_admin(self):
        if messagebox.askyesno("Xác nhận", "Hủy đăng ký context menu?"):
            success, message = self.manager.unregister_context_menu(force_admin=True)
            if success:
                messagebox.showinfo("Thành công", f"✅ {message}")
            else:
                messagebox.showerror("Lỗi", f"❌ {message}")
            self.update_status()
    
    def run(self):
        self.root.mainloop()

def main():
    print("🚀 Khởi động Expandable GUI...")
    try:
        app = ExpandableGUI()
        app.run()
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
