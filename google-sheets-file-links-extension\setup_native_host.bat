@echo off
setlocal

REM --- C<PERSON><PERSON> hình ---
set HOST_NAME=com.my_company.gsheets_file_opener
set PYTHON_SCRIPT_NAME=native_host_app.py
set HOST_MANIFEST_NAME=%HOST_NAME%.json

REM Đường dẫn đến thư mục chứa script này (thư mục gốc của extension)
set SCRIPT_DIR=%~dp0
REM Chuẩn hóa đường dẫn (loại bỏ dấu \ ở cuối nếu có)
if "%SCRIPT_DIR:~-1%"=="\" set SCRIPT_DIR=%SCRIPT_DIR:~0,-1%

REM Đường dẫn đến các file nguồn trong thư mục extension
set SOURCE_PYTHON_SCRIPT=%SCRIPT_DIR%\%PYTHON_SCRIPT_NAME%
set SOURCE_HOST_MANIFEST=%SCRIPT_DIR%\%HOST_MANIFEST_NAME%

REM Đường dẫn cài đặt ứng dụng gốc (sử dụng APPDATA để không cần quyền admin cho việc ghi file)
REM Chrome tìm manifest trong HKCU hoặc HKLM. HKCU không cần admin.
set TARGET_DIR=%APPDATA%\%HOST_NAME%
set TARGET_PYTHON_SCRIPT=%TARGET_DIR%\%PYTHON_SCRIPT_NAME%
set TARGET_HOST_MANIFEST=%TARGET_DIR%\%HOST_MANIFEST_NAME%

REM --- Bắt đầu cài đặt ---
echo Dang cai dat Native Host cho %HOST_NAME%...

REM 1. Tạo thư mục đích nếu chưa tồn tại
echo Tao thu muc dich: %TARGET_DIR%
if not exist "%TARGET_DIR%" (
    mkdir "%TARGET_DIR%"
    if errorlevel 1 (
        echo Loi: Khong the tao thu muc %TARGET_DIR%. Vui long thu chay script voi quyen Administrator.
        goto :eof
    )
)

REM 2. Sao chép script Python vào thư mục đích
echo Sao chep script Python: %SOURCE_PYTHON_SCRIPT% toi %TARGET_PYTHON_SCRIPT%
copy /Y "%SOURCE_PYTHON_SCRIPT%" "%TARGET_PYTHON_SCRIPT%" > nul
if errorlevel 1 (
    echo Loi: Khong the sao chep %PYTHON_SCRIPT_NAME%.
    goto :eof
)

REM 3. Sao chép tệp manifest của ứng dụng gốc vào thư mục đích
echo Sao chep manifest cua ung dung goc: %SOURCE_HOST_MANIFEST% toi %TARGET_HOST_MANIFEST%
copy /Y "%SOURCE_HOST_MANIFEST%" "%TARGET_HOST_MANIFEST%" > nul
if errorlevel 1 (
    echo Loi: Khong the sao chep %HOST_MANIFEST_NAME%.
    goto :eof
)

REM 4. Cập nhật đường dẫn "path" trong tệp manifest đích để trỏ đúng đến script Python
echo Cap nhat duong dan trong manifest...
powershell -Command "(Get-Content '%TARGET_HOST_MANIFEST%' -Raw) -replace '\""path\"": \"".*?\""', '\""path\"": \""%TARGET_PYTHON_SCRIPT:\=\\%\""' | Set-Content '%TARGET_HOST_MANIFEST%'"
if errorlevel 1 (
    echo Loi: Khong the cap nhat duong dan trong %TARGET_HOST_MANIFEST%.
    goto :eof
)
echo.
echo --- LUU Y QUAN TRONG ---
echo Ban can cap nhat gia tri "YOUR_EXTENSION_ID_HERE" trong file:
echo %TARGET_HOST_MANIFEST%
echo bang ID thuc te cua extension sau khi tai no len Chrome.
echo Ban co the tim ID nay tai trang chrome://extensions (bat che do Developer Mode).
echo Vi du: "chrome-extension://abcdefghijklmnopabcdefghijklmnop/"
echo.

REM 5. Đăng ký ứng dụng gốc với Chrome bằng cách tạo khóa registry
REM Đăng ký cho người dùng hiện tại (HKCU)
set REG_KEY_PATH="HKCU\Software\Google\Chrome\NativeMessagingHosts\%HOST_NAME%"
echo Dang ky ung dung goc voi Chrome (HKCU)...
reg add %REG_KEY_PATH% /ve /t REG_SZ /d "%TARGET_HOST_MANIFEST%" /f
if errorlevel 1 (
    echo Loi: Khong the tao khoa registry. Neu loi tiep tuc, thu chay script voi quyen Administrator.
) else (
    echo Da dang ky ung dung goc thanh cong cho nguoi dung hien tai.
)

echo.
echo --- HOAN TAT ---
echo Cai dat Native Host hoan tat.
echo.
echo Vui long thuc hien cac buoc sau:
echo 1. Mo Google Chrome va di den trang chrome://extensions
echo 2. Bat "Developer mode" (Che do nha phat trien).
echo 3. Nhap "Load unpacked" (Tai tien ich da giai nen) va chon thu muc "%SCRIPT_DIR%".
echo 4. Tim ID cua extension vua tai (vi du: abcdefghijklmnopabcdefghijklmnop).
echo 5. Mo file "%TARGET_HOST_MANIFEST%" (duong dan da hien thi o tren).
echo 6. Thay the "YOUR_EXTENSION_ID_HERE" bang ID extension ban vua tim duoc. Luu file.
echo 7. Khoi dong lai Google Chrome.
echo.
echo Neu ban muon go cai dat, hay chay script uninstall_native_host.bat (se duoc tao sau).

endlocal
pause
