<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tạo Icons cho Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-item canvas {
            border: 1px solid #ccc;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #3367d6;
        }
        .instructions {
            background: #e8f0fe;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1a73e8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tạo Icons cho Google Sheets File Links Extension</h1>
        
        <div class="instructions">
            <h3>Hướng dẫn:</h3>
            <ol>
                <li>Trang này sẽ tự động tạo các icon cần thiết cho extension</li>
                <li>Nhấp vào nút "Tải xuống" để lưu từng icon</li>
                <li>Tạo thư mục <code>icons</code> trong thư mục extension</li>
                <li>Lưu các file icon vào thư mục <code>icons</code> với đúng tên</li>
            </ol>
        </div>
        
        <div class="icon-preview" id="iconPreview">
            <!-- Icons sẽ được tạo bằng JavaScript -->
        </div>
        
        <button onclick="downloadAllIcons()" style="background: #34a853; color: white; border: none; padding: 12px 24px; border-radius: 4px; cursor: pointer; font-size: 14px; margin-top: 20px;">
            Tải xuống tất cả Icons
        </button>
    </div>

    <script>
        // Tạo icon với kích thước khác nhau
        const iconSizes = [16, 32, 48, 128];
        const icons = [];

        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#4285f4');
            gradient.addColorStop(1, '#34a853');
            
            // Draw background
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Draw border
            ctx.strokeStyle = '#1a73e8';
            ctx.lineWidth = Math.max(1, size / 32);
            ctx.strokeRect(0, 0, size, size);
            
            // Draw file icon
            const iconSize = size * 0.6;
            const x = (size - iconSize) / 2;
            const y = (size - iconSize) / 2;
            
            // File body
            ctx.fillStyle = 'white';
            ctx.fillRect(x, y + iconSize * 0.15, iconSize * 0.8, iconSize * 0.7);
            
            // File corner fold
            ctx.beginPath();
            ctx.moveTo(x + iconSize * 0.6, y + iconSize * 0.15);
            ctx.lineTo(x + iconSize * 0.8, y + iconSize * 0.15);
            ctx.lineTo(x + iconSize * 0.8, y + iconSize * 0.35);
            ctx.closePath();
            ctx.fillStyle = '#e8eaed';
            ctx.fill();
            
            // File lines
            ctx.strokeStyle = '#5f6368';
            ctx.lineWidth = Math.max(1, size / 64);
            for (let i = 0; i < 3; i++) {
                const lineY = y + iconSize * (0.4 + i * 0.1);
                ctx.beginPath();
                ctx.moveTo(x + iconSize * 0.15, lineY);
                ctx.lineTo(x + iconSize * 0.65, lineY);
                ctx.stroke();
            }
            
            // Link symbol
            ctx.strokeStyle = '#1a73e8';
            ctx.lineWidth = Math.max(2, size / 16);
            ctx.beginPath();
            ctx.arc(x + iconSize * 0.2, y + iconSize * 0.8, iconSize * 0.08, 0, Math.PI * 2);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.arc(x + iconSize * 0.4, y + iconSize * 0.8, iconSize * 0.08, 0, Math.PI * 2);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(x + iconSize * 0.25, y + iconSize * 0.75);
            ctx.lineTo(x + iconSize * 0.35, y + iconSize * 0.85);
            ctx.stroke();
            
            return canvas;
        }

        function createIconPreview() {
            const container = document.getElementById('iconPreview');
            
            iconSizes.forEach(size => {
                const canvas = createIcon(size);
                icons.push({ size, canvas });
                
                const item = document.createElement('div');
                item.className = 'icon-item';
                
                const title = document.createElement('h4');
                title.textContent = `${size}x${size}`;
                title.style.margin = '0 0 10px 0';
                
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = 'Tải xuống';
                downloadBtn.onclick = () => downloadIcon(canvas, `icon${size}.png`);
                
                item.appendChild(title);
                item.appendChild(canvas);
                item.appendChild(document.createElement('br'));
                item.appendChild(downloadBtn);
                
                container.appendChild(item);
            });
        }

        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadAllIcons() {
            icons.forEach(({ size, canvas }) => {
                setTimeout(() => {
                    downloadIcon(canvas, `icon${size}.png`);
                }, size * 10); // Delay để tránh download đồng thời
            });
        }

        // Tạo icons khi trang load
        window.onload = createIconPreview;
    </script>
</body>
</html>
