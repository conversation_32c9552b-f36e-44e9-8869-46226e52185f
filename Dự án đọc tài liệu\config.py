import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Google Cloud Vision API configuration
GOOGLE_CLOUD_PROJECT = os.getenv("GOOGLE_CLOUD_PROJECT", "soy-tube-461903")
GOOGLE_APPLICATION_CREDENTIALS = os.getenv(
    "GOOGLE_APPLICATION_CREDENTIALS",
    r"G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\soy-tube-461903-r0-5118a4d80a52.json"
)

# AI API Keys
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
GROK_API_KEY = os.getenv("GROK_API_KEY", "")

# AI Model Names (default)
AI_MODELS = {
    "Gemini": "gemini-pro",
    "OpenAI": "gpt-3.5-turbo",
    "Grok": "grok-1"
}

# Set environment variables
os.environ["GOOGLE_CLOUD_PROJECT"] = GOOGLE_CLOUD_PROJECT
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = GOOGLE_APPLICATION_CREDENTIALS
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY 