import sys
import os
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox
import json
from pathlib import Path
from python_version_manager import PythonVersionManager

class ProjectLauncher:
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.python_manager = PythonVersionManager()
        self.config_file = os.path.join(self.script_dir, 'launcher_config.json')
        self.load_config()
        
        # Danh sách các script trong dự án
        self.project_scripts = {
            'main.py': {
                'name': 'Ứng dụng chính - PDF OCR và Tóm tắt',
                'description': 'Giao diện chính để xử lý PDF, OCR và tóm tắt văn bản',
                'requires_gui': True
            },
            'main.pyw': {
                'name': 'Ứng dụng chính (Ẩn Terminal)',
                'description': '<PERSON>ia<PERSON> diện chính không hiển thị cửa sổ terminal',
                'requires_gui': True
            },
            'config_manager.py': {
                'name': '<PERSON>u<PERSON>n lý cấu hình',
                'description': '<PERSON><PERSON><PERSON> hình API keys, Google Sheet và các thiết lập khác',
                'requires_gui': True
            },
            'rename_pdf.py': {
                'name': 'Đổi tên PDF (Command Line)',
                'description': 'Script để đổi tên PDF và lưu vào Google Sheet (chạy từ context menu)',
                'requires_gui': False
            },
            'rename_pdf.pyw': {
                'name': 'Đổi tên PDF (Ẩn Terminal)',
                'description': 'Script đổi tên PDF không hiển thị cửa sổ terminal',
                'requires_gui': False
            },
            'python_version_manager.py': {
                'name': 'Quản lý phiên bản Python',
                'description': 'Quản lý và chọn phiên bản Python để chạy các script',
                'requires_gui': True
            },
            'register_pdf_context_menu.py': {
                'name': 'Đăng ký Context Menu',
                'description': 'Đăng ký menu chuột phải cho file PDF',
                'requires_gui': True
            },
            'view_debug_log.py': {
                'name': 'Xem Debug Log',
                'description': 'Xem và phân tích log debug của các script',
                'requires_gui': True
            },
            'test_vision_extraction.py': {
                'name': 'Test Vision Extraction',
                'description': 'Test trích xuất thông tin từ ảnh PDF với AI Vision',
                'requires_gui': True
            },
            'progress_dialog.py': {
                'name': 'Test Progress Dialog (Advanced)',
                'description': 'Test giao diện progress dialog phức tạp',
                'requires_gui': True
            },
            'simple_progress.py': {
                'name': 'Test Simple Progress',
                'description': 'Test giao diện progress dialog đơn giản',
                'requires_gui': True
            },
            'test_progress_simple.py': {
                'name': 'Test Progress System',
                'description': 'Test toàn bộ hệ thống progress dialog',
                'requires_gui': True
            },
            'test_openai_api.py': {
                'name': 'Test OpenAI API',
                'description': 'Test và debug OpenAI API connection',
                'requires_gui': False
            },
            'fix_openai_config.py': {
                'name': 'Fix OpenAI Config',
                'description': 'Sửa lỗi OpenAI API và cập nhật models',
                'requires_gui': False
            },
            'check_openai_version.py': {
                'name': 'Check OpenAI Version',
                'description': 'Kiểm tra phiên bản OpenAI và tương thích',
                'requires_gui': False
            },
            'test_all_apis_latest.py': {
                'name': 'Test All APIs (Latest)',
                'description': 'Test tất cả APIs với phiên bản mới nhất',
                'requires_gui': False
            },
            'fix_openai_chat_error.py': {
                'name': 'Fix OpenAI Chat Error',
                'description': 'Sửa lỗi OpenAI "no attribute chat"',
                'requires_gui': False
            },
            'fix_grok_proxies_error.py': {
                'name': 'Fix Grok Proxies Error',
                'description': 'Sửa lỗi Grok "unexpected keyword argument proxies"',
                'requires_gui': False
            },
            'check_api_keys.py': {
                'name': 'Check & Setup API Keys',
                'description': 'Kiểm tra và cấu hình API keys cho tất cả providers',
                'requires_gui': False
            }
        }
        
    def load_config(self):
        """Load cấu hình launcher"""
        default_config = {
            'default_python': '',
            'last_used_scripts': [],
            'window_geometry': '800x600'
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
            except Exception as e:
                print(f"Lỗi khi đọc config: {e}")
        
        self.config = default_config
        
    def save_config(self):
        """Lưu cấu hình launcher"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"Lỗi khi lưu config: {e}")
    
    def get_script_path(self, script_name):
        """Lấy đường dẫn đầy đủ của script"""
        return os.path.join(self.script_dir, script_name)
    
    def run_script(self, script_name, python_version=None, args=None):
        """Chạy script với phiên bản Python được chọn"""
        script_path = self.get_script_path(script_name)
        
        if not os.path.exists(script_path):
            return False, f"Script không tồn tại: {script_path}"
        
        # Sử dụng phiên bản Python được chọn hoặc mặc định
        if python_version:
            python_path = self.python_manager.get_python_path(python_version)
            if not python_path:
                return False, f"Không tìm thấy phiên bản Python: {python_version}"
        else:
            python_path = sys.executable
        
        try:
            cmd = [python_path, script_path]
            if args:
                cmd.extend(args)
            
            # Chạy script trong background
            if os.name == 'nt':  # Windows
                subprocess.Popen(cmd, creationflags=subprocess.CREATE_NEW_CONSOLE)
            else:
                subprocess.Popen(cmd)
            
            return True, f"Đã khởi chạy {script_name}"
        except Exception as e:
            return False, f"Lỗi khi chạy script: {e}"

class LauncherGUI:
    def __init__(self):
        self.launcher = ProjectLauncher()
        self.root = tk.Tk()
        self.root.title("Project Launcher - Dự án đọc tài liệu")
        self.root.geometry(self.launcher.config.get('window_geometry', '800x600'))
        self.setup_ui()
        
    def setup_ui(self):
        # Style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        header_frame.columnconfigure(0, weight=1)
        
        title_label = ttk.Label(header_frame, text="Dự án đọc tài liệu", 
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0)
        
        subtitle_label = ttk.Label(header_frame, 
                                  text="Launcher để quản lý và chạy các script trong dự án",
                                  font=("Arial", 10))
        subtitle_label.grid(row=1, column=0, pady=(5, 0))
        
        # Python version selection
        python_frame = ttk.LabelFrame(main_frame, text="Chọn phiên bản Python", padding="10")
        python_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        python_frame.columnconfigure(1, weight=1)
        
        ttk.Label(python_frame, text="Phiên bản Python:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.python_var = tk.StringVar()
        self.python_combo = ttk.Combobox(python_frame, textvariable=self.python_var, state="readonly")
        self.python_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        refresh_btn = ttk.Button(python_frame, text="Làm mới", command=self.refresh_python_versions)
        refresh_btn.grid(row=0, column=2)
        
        manage_btn = ttk.Button(python_frame, text="Quản lý Python", command=self.open_python_manager)
        manage_btn.grid(row=0, column=3, padx=(5, 0))
        
        # Scripts list
        scripts_frame = ttk.LabelFrame(main_frame, text="Danh sách Script", padding="10")
        scripts_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scripts_frame.columnconfigure(0, weight=1)
        scripts_frame.rowconfigure(0, weight=1)
        
        # Treeview for scripts
        columns = ('script', 'name', 'description')
        self.scripts_tree = ttk.Treeview(scripts_frame, columns=columns, show='headings', height=10)
        self.scripts_tree.heading('script', text='File')
        self.scripts_tree.heading('name', text='Tên')
        self.scripts_tree.heading('description', text='Mô tả')
        
        self.scripts_tree.column('script', width=150)
        self.scripts_tree.column('name', width=250)
        self.scripts_tree.column('description', width=350)
        
        self.scripts_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar for treeview
        scripts_scrollbar = ttk.Scrollbar(scripts_frame, orient=tk.VERTICAL, command=self.scripts_tree.yview)
        scripts_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.scripts_tree.configure(yscrollcommand=scripts_scrollbar.set)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(20, 0))
        
        ttk.Button(buttons_frame, text="Chạy Script", command=self.run_selected_script).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="Mở thư mục", command=self.open_project_folder).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="Cấu hình", command=self.open_config_manager).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="Context Menu", command=self.open_context_menu_manager).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="Xem Logs", command=self.open_log_viewer).pack(side=tk.LEFT, padx=(0, 10))
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Sẵn sàng")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Load data
        self.refresh_python_versions()
        self.load_scripts()
        
        # Bind events
        self.scripts_tree.bind('<Double-1>', lambda e: self.run_selected_script())
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def refresh_python_versions(self):
        """Làm mới danh sách phiên bản Python"""
        self.launcher.python_manager.detect_python_versions()
        versions = list(self.launcher.python_manager.get_python_versions().keys())
        
        self.python_combo['values'] = ["Mặc định (System)"] + versions
        
        # Chọn phiên bản mặc định
        default_python = self.launcher.config.get('default_python', '')
        if default_python and default_python in versions:
            self.python_var.set(default_python)
        else:
            self.python_var.set("Mặc định (System)")
    
    def load_scripts(self):
        """Load danh sách scripts vào treeview"""
        for item in self.scripts_tree.get_children():
            self.scripts_tree.delete(item)
        
        for script_file, info in self.launcher.project_scripts.items():
            script_path = self.launcher.get_script_path(script_file)
            status = "✓" if os.path.exists(script_path) else "✗"
            
            self.scripts_tree.insert('', tk.END, values=(
                f"{status} {script_file}",
                info['name'],
                info['description']
            ))
    
    def run_selected_script(self):
        """Chạy script được chọn"""
        selected = self.scripts_tree.selection()
        if not selected:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một script để chạy")
            return
        
        item = self.scripts_tree.item(selected[0])
        script_display = item['values'][0]
        script_name = script_display.split(' ', 1)[1]  # Bỏ status symbol
        
        # Kiểm tra file tồn tại
        script_path = self.launcher.get_script_path(script_name)
        if not os.path.exists(script_path):
            messagebox.showerror("Lỗi", f"Script không tồn tại: {script_name}")
            return
        
        # Lấy phiên bản Python được chọn
        python_version = None
        if self.python_var.get() != "Mặc định (System)":
            python_version = self.python_var.get()
        
        # Chạy script
        success, message = self.launcher.run_script(script_name, python_version)
        
        if success:
            self.status_var.set(message)
            # Lưu vào lịch sử
            if script_name not in self.launcher.config['last_used_scripts']:
                self.launcher.config['last_used_scripts'].append(script_name)
                if len(self.launcher.config['last_used_scripts']) > 10:
                    self.launcher.config['last_used_scripts'].pop(0)
            self.launcher.config['default_python'] = self.python_var.get()
            self.launcher.save_config()
        else:
            messagebox.showerror("Lỗi", message)
            self.status_var.set("Lỗi khi chạy script")
    
    def open_project_folder(self):
        """Mở thư mục dự án"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.launcher.script_dir)
            else:  # Linux/Mac
                subprocess.Popen(['xdg-open', self.launcher.script_dir])
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể mở thư mục: {e}")
    
    def open_python_manager(self):
        """Mở Python Version Manager"""
        success, message = self.launcher.run_script('python_version_manager.py')
        if not success:
            messagebox.showerror("Lỗi", message)
    
    def open_config_manager(self):
        """Mở Config Manager"""
        success, message = self.launcher.run_script('config_manager.py')
        if not success:
            messagebox.showerror("Lỗi", message)
    
    def open_context_menu_manager(self):
        """Mở Context Menu Manager"""
        success, message = self.launcher.run_script('register_pdf_context_menu.py')
        if not success:
            messagebox.showerror("Lỗi", message)

    def open_log_viewer(self):
        """Mở Log Viewer"""
        success, message = self.launcher.run_script('view_debug_log.py')
        if not success:
            messagebox.showerror("Lỗi", message)
    
    def on_closing(self):
        """Xử lý khi đóng cửa sổ"""
        # Lưu geometry
        self.launcher.config['window_geometry'] = self.root.geometry()
        self.launcher.save_config()
        self.root.destroy()
    
    def run(self):
        """Chạy GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    app = LauncherGUI()
    app.run()
