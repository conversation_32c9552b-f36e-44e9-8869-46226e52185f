import os
import sys
import winreg
import tkinter as tk
from tkinter import messagebox, filedialog
import json
import subprocess
import ctypes
from pathlib import Path

class PDFContextMenuManager:
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.config_file = os.path.join(self.script_dir, 'context_menu_config.json')
        self.config_changed = False  # Theo dõi thay đổi cấu hình
        self.load_config()

    def is_admin(self):
        """Kiểm tra xem có quyền admin không"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def run_as_admin(self, action):
        """Chạy lại script với quyền admin"""
        try:
            if action == "register":
                params = f'"{sys.executable}" "{__file__}" register_admin'
            elif action == "unregister":
                params = f'"{sys.executable}" "{__file__}" unregister_admin'
            else:
                return False, "Hành động không hợp lệ"

            result = ctypes.windll.shell32.ShellExecuteW(
                None, "runas", "cmd.exe", f"/c {params}", None, 1
            )

            if result > 32:  # Success
                return True, f"Đã yêu cầu quyền admin để {action}"
            else:
                return False, "Người dùng từ chối cấp quyền admin"

        except Exception as e:
            return False, f"Lỗi khi yêu cầu quyền admin: {str(e)}"
        
    def normalize_path(self, path):
        """Chuẩn hóa đường dẫn sử dụng \\ thay vì /"""
        if path:
            # Chuyển đổi tất cả / thành \\
            normalized = path.replace('/', '\\')
            # Đảm bảo đường dẫn tuyệt đối
            if not os.path.isabs(normalized):
                normalized = os.path.abspath(normalized)
            return normalized.replace('/', '\\')
        return path

    def load_config(self):
        """Load cấu hình context menu"""
        # Sử dụng pythonw.exe để ẩn terminal
        python_exe = sys.executable
        if python_exe.endswith('python.exe'):
            pythonw_exe = python_exe.replace('python.exe', 'pythonw.exe')
            if os.path.exists(pythonw_exe):
                python_exe = pythonw_exe

        # Chuẩn hóa đường dẫn với \\
        python_exe = self.normalize_path(python_exe)
        rename_script_path = self.normalize_path(os.path.join(self.script_dir, 'rename_pdf.py'))
        vbs_script_path = self.normalize_path(os.path.join(self.script_dir, 'run_rename_pdf_hidden.vbs'))

        default_config = {
            'python_path': python_exe,
            'rename_script_path': rename_script_path,
            'vbs_script_path': vbs_script_path,
            'menu_text': 'Đổi tên PDF và lưu Google Sheet',
            'icon_path': '',
            'use_vbs': True  # Sử dụng VBS để ẩn terminal
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
            except Exception as e:
                print(f"Lỗi khi đọc config: {e}")
        
        self.config = default_config
        self.save_config()
    
    def save_config(self):
        """Lưu cấu hình context menu với đường dẫn chuẩn hóa"""
        try:
            # Chuẩn hóa tất cả đường dẫn trước khi lưu
            config_to_save = self.config.copy()

            # Chuẩn hóa các đường dẫn
            if 'python_path' in config_to_save:
                config_to_save['python_path'] = self.normalize_path(config_to_save['python_path'])
            if 'rename_script_path' in config_to_save:
                config_to_save['rename_script_path'] = self.normalize_path(config_to_save['rename_script_path'])
            if 'vbs_script_path' in config_to_save:
                config_to_save['vbs_script_path'] = self.normalize_path(config_to_save['vbs_script_path'])
            if 'icon_path' in config_to_save:
                config_to_save['icon_path'] = self.normalize_path(config_to_save['icon_path'])

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=4, ensure_ascii=False)

            # Cập nhật config hiện tại với đường dẫn đã chuẩn hóa
            self.config = config_to_save
            self.config_changed = False  # Reset flag sau khi lưu
            print(f"Đã lưu cấu hình vào: {self.config_file}")
        except Exception as e:
            print(f"Lỗi khi lưu config: {e}")

    def update_config(self, key, value):
        """Cập nhật cấu hình và đánh dấu có thay đổi"""
        # Chuẩn hóa đường dẫn nếu là path
        if key in ['python_path', 'rename_script_path', 'vbs_script_path', 'icon_path']:
            value = self.normalize_path(value)

        if self.config.get(key) != value:
            self.config[key] = value
            self.config_changed = True
            print(f"Cấu hình '{key}' đã được thay đổi")

    def has_unsaved_changes(self):
        """Kiểm tra có thay đổi chưa lưu không"""
        return self.config_changed

    def auto_save_if_changed(self):
        """Tự động lưu nếu có thay đổi"""
        if self.config_changed:
            self.save_config()
            return True
        return False
    
    def register_context_menu(self, force_admin=False):
        """Đăng ký context menu cho file PDF"""
        # Kiểm tra quyền admin
        if not self.is_admin():
            if force_admin:
                return self.run_as_admin("register")
            else:
                return False, "Cần quyền Administrator để đăng ký context menu."

        try:
            # Tạo key cho PDF files
            pdf_key_path = r"*\shell\RenamePDF"

            # Tạo key chính
            with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path) as key:
                winreg.SetValue(key, "", winreg.REG_SZ, self.config['menu_text'])

                # Thêm icon nếu có
                if self.config['icon_path'] and os.path.exists(self.config['icon_path']):
                    winreg.SetValueEx(key, "Icon", 0, winreg.REG_SZ, self.config['icon_path'])

            # Tạo command key
            command_key_path = pdf_key_path + r"\command"
            with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, command_key_path) as key:
                if self.config.get('use_vbs', False) and os.path.exists(self.config.get('vbs_script_path', '')):
                    # Sử dụng VBS để ẩn terminal
                    command = f'wscript.exe "{self.config["vbs_script_path"]}" "%1"'
                else:
                    # Sử dụng pythonw.exe để ẩn terminal
                    command = f'"{self.config["python_path"]}" "{self.config["rename_script_path"]}" "%1"'
                winreg.SetValue(key, "", winreg.REG_SZ, command)

            return True, "Đã đăng ký context menu thành công!"

        except PermissionError:
            return False, "Cần quyền Administrator để đăng ký context menu."
        except Exception as e:
            return False, f"Lỗi khi đăng ký context menu: {str(e)}"
    
    def unregister_context_menu(self, force_admin=False):
        """Hủy đăng ký context menu"""
        # Kiểm tra quyền admin
        if not self.is_admin():
            if force_admin:
                return self.run_as_admin("unregister")
            else:
                return False, "Cần quyền Administrator để hủy đăng ký context menu."

        try:
            pdf_key_path = r"*\shell\RenamePDF"
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path + r"\command")
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path)
            return True, "Đã hủy đăng ký context menu thành công!"
        except FileNotFoundError:
            return False, "Context menu chưa được đăng ký."
        except PermissionError:
            return False, "Cần quyền Administrator để hủy đăng ký context menu."
        except Exception as e:
            return False, f"Lỗi khi hủy đăng ký context menu: {str(e)}"
    
    def is_registered(self):
        """Kiểm tra xem context menu đã được đăng ký chưa"""
        try:
            with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF"):
                return True
        except FileNotFoundError:
            return False
        except Exception:
            return False

class ContextMenuGUI:
    def __init__(self):
        self.manager = PDFContextMenuManager()
        self.root = tk.Tk()
        self.root.title("PDF Context Menu Manager")
        self.root.geometry("600x450")  # Tăng chiều cao để chứa thêm thông tin
        self.root.resizable(False, False)

        # Theo dõi thay đổi
        self.setup_change_tracking()
        self.setup_ui()

        # Xử lý khi đóng cửa sổ
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_ui(self):
        # Frame chính
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Tiêu đề
        title_label = tk.Label(main_frame, text="Quản lý Context Menu cho PDF", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Trạng thái hiện tại
        status_frame = tk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        tk.Label(status_frame, text="Trạng thái hiện tại:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        self.status_label = tk.Label(status_frame, text="", fg="blue")
        self.status_label.pack(anchor=tk.W)
        
        # Cấu hình
        config_frame = tk.LabelFrame(main_frame, text="Cấu hình", padx=10, pady=10)
        config_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Python path
        python_frame = tk.Frame(config_frame)
        python_frame.pack(fill=tk.X, pady=(0, 10))
        tk.Label(python_frame, text="Đường dẫn Python:").pack(anchor=tk.W)
        python_entry_frame = tk.Frame(python_frame)
        python_entry_frame.pack(fill=tk.X, pady=(5, 0))
        self.python_path_var = tk.StringVar(value=self.manager.config['python_path'])
        self.python_path_var.trace_add('write', self.on_config_change)  # Theo dõi thay đổi
        self.python_path_entry = tk.Entry(python_entry_frame, textvariable=self.python_path_var)
        self.python_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(python_entry_frame, text="Browse",
                 command=self.browse_python).pack(side=tk.RIGHT, padx=(5, 0))

        # Script path
        script_frame = tk.Frame(config_frame)
        script_frame.pack(fill=tk.X, pady=(0, 10))
        tk.Label(script_frame, text="Đường dẫn Script:").pack(anchor=tk.W)
        script_entry_frame = tk.Frame(script_frame)
        script_entry_frame.pack(fill=tk.X, pady=(5, 0))
        self.script_path_var = tk.StringVar(value=self.manager.config['rename_script_path'])
        self.script_path_var.trace_add('write', self.on_config_change)  # Theo dõi thay đổi
        self.script_path_entry = tk.Entry(script_entry_frame, textvariable=self.script_path_var)
        self.script_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(script_entry_frame, text="Browse",
                 command=self.browse_script).pack(side=tk.RIGHT, padx=(5, 0))

        # Menu text
        menu_frame = tk.Frame(config_frame)
        menu_frame.pack(fill=tk.X, pady=(0, 10))
        tk.Label(menu_frame, text="Tên hiển thị trong menu:").pack(anchor=tk.W)
        self.menu_text_var = tk.StringVar(value=self.manager.config['menu_text'])
        self.menu_text_var.trace_add('write', self.on_config_change)  # Theo dõi thay đổi
        self.menu_text_entry = tk.Entry(menu_frame, textvariable=self.menu_text_var)
        self.menu_text_entry.pack(fill=tk.X, pady=(5, 0))

        # Icon path (optional)
        icon_frame = tk.Frame(config_frame)
        icon_frame.pack(fill=tk.X, pady=(0, 10))
        tk.Label(icon_frame, text="Đường dẫn Icon (tùy chọn):").pack(anchor=tk.W)
        icon_entry_frame = tk.Frame(icon_frame)
        icon_entry_frame.pack(fill=tk.X, pady=(5, 0))
        self.icon_path_var = tk.StringVar(value=self.manager.config.get('icon_path', ''))
        self.icon_path_var.trace_add('write', self.on_config_change)  # Theo dõi thay đổi
        self.icon_path_entry = tk.Entry(icon_entry_frame, textvariable=self.icon_path_var)
        self.icon_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(icon_entry_frame, text="Browse",
                 command=self.browse_icon).pack(side=tk.RIGHT, padx=(5, 0))

        # Hide terminal option
        hide_frame = tk.Frame(config_frame)
        hide_frame.pack(fill=tk.X, pady=(0, 10))
        self.use_vbs_var = tk.BooleanVar(value=self.manager.config.get('use_vbs', True))
        self.use_vbs_var.trace_add('write', self.on_config_change)  # Theo dõi thay đổi
        self.use_vbs_checkbox = tk.Checkbutton(hide_frame,
                                              text="Ẩn cửa sổ terminal khi chạy (khuyến nghị)",
                                              variable=self.use_vbs_var)
        self.use_vbs_checkbox.pack(anchor=tk.W)
        
        # Buttons - Chia thành 2 hàng
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        # Hàng 1: Các nút cấu hình
        config_button_frame = tk.Frame(button_frame)
        config_button_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Button(config_button_frame, text="Lưu cấu hình",
                 command=self.save_config, bg="lightblue").pack(side=tk.LEFT, padx=(0, 5))
        tk.Button(config_button_frame, text="Tự động lưu",
                 command=self.auto_save, bg="lightyellow").pack(side=tk.LEFT, padx=(0, 5))
        tk.Button(config_button_frame, text="Lưu thay đổi",
                 command=self.save_changes, bg="lightcyan").pack(side=tk.LEFT, padx=(0, 10))
        tk.Button(config_button_frame, text="Kiểm tra trạng thái",
                 command=self.check_status).pack(side=tk.LEFT)

        # Hàng 2: Các nút context menu (cần admin)
        admin_button_frame = tk.Frame(button_frame)
        admin_button_frame.pack(fill=tk.X)

        tk.Button(admin_button_frame, text="Đăng ký Context Menu (Admin)",
                 command=self.register_menu_admin, bg="lightgreen").pack(side=tk.LEFT, padx=(0, 5))
        tk.Button(admin_button_frame, text="Hủy đăng ký (Admin)",
                 command=self.unregister_menu_admin, bg="lightcoral").pack(side=tk.LEFT, padx=(0, 5))

        # Hiển thị trạng thái admin
        admin_status = "✅ Admin" if self.manager.is_admin() else "❌ Không có quyền Admin"
        tk.Label(admin_button_frame, text=admin_status,
                fg="green" if self.manager.is_admin() else "red").pack(side=tk.RIGHT)
        
        # Hướng dẫn
        help_frame = tk.LabelFrame(main_frame, text="Hướng dẫn", padx=10, pady=10)
        help_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        help_text = """1. Cấu hình đường dẫn Python và script rename_pdf.py
2. Nhập tên hiển thị cho menu context
3. Tùy chọn: Chọn icon cho menu
4. Nhấn "Lưu thay đổi" để lưu và chuẩn hóa đường dẫn (sử dụng \\)
5. Nhấn "Đăng ký Context Menu (Admin)" để thêm menu vào chuột phải
6. Sau khi đăng ký, bạn có thể nhấn chuột phải vào file PDF và chọn menu đã tạo

Tính năng mới:
• Chuẩn hóa đường dẫn tự động (sử dụng \\ thay vì /)
• Theo dõi thay đổi tự động - hiển thị trạng thái thay đổi chưa lưu
• Tự động lưu khi có thay đổi
• Cảnh báo khi đóng cửa sổ mà có thay đổi chưa lưu
• Quản lý quyền Administrator tự động

Các nút chức năng:
• "Lưu cấu hình": Lưu cơ bản
• "Tự động lưu": Lưu nếu có thay đổi
• "Lưu thay đổi": Lưu và chuẩn hóa đường dẫn
• "Đăng ký Context Menu (Admin)": Tự động yêu cầu quyền admin
• "Hủy đăng ký (Admin)": Tự động yêu cầu quyền admin

Lưu ý: Các thao tác đăng ký/hủy đăng ký sẽ tự động yêu cầu quyền Administrator."""
        
        help_label = tk.Label(help_frame, text=help_text, justify=tk.LEFT, wraplength=550)
        help_label.pack(anchor=tk.W)
        
        # Thêm thông tin về thay đổi
        self.changes_frame = tk.LabelFrame(main_frame, text="Trạng thái thay đổi", padx=10, pady=10)
        self.changes_frame.pack(fill=tk.X, pady=(10, 0))

        self.changes_label = tk.Label(self.changes_frame, text="", fg="orange")
        self.changes_label.pack(anchor=tk.W)

        # Kiểm tra trạng thái ban đầu
        self.check_status()
        self.update_changes_status()
    
    def browse_python(self):
        """Chọn file Python executable"""
        file_path = filedialog.askopenfilename(
            title="Chọn Python executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if file_path:
            # Chuẩn hóa đường dẫn với \\
            normalized_path = self.manager.normalize_path(file_path)
            self.python_path_var.set(normalized_path)

    def browse_script(self):
        """Chọn file script Python"""
        file_path = filedialog.askopenfilename(
            title="Chọn rename_pdf.py",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")]
        )
        if file_path:
            # Chuẩn hóa đường dẫn với \\
            normalized_path = self.manager.normalize_path(file_path)
            self.script_path_var.set(normalized_path)

    def browse_icon(self):
        """Chọn file icon"""
        file_path = filedialog.askopenfilename(
            title="Chọn file icon",
            filetypes=[("Icon files", "*.ico"), ("Image files", "*.png;*.jpg;*.bmp"), ("All files", "*.*")]
        )
        if file_path:
            # Chuẩn hóa đường dẫn với \\
            normalized_path = self.manager.normalize_path(file_path)
            self.icon_path_var.set(normalized_path)
    
    def setup_change_tracking(self):
        """Thiết lập theo dõi thay đổi"""
        # Sẽ được thiết lập sau khi tạo các StringVar
        pass

    def on_config_change(self, *args):
        """Callback khi có thay đổi cấu hình"""
        # Cập nhật config và đánh dấu thay đổi
        try:
            self.manager.update_config('python_path', self.python_path_var.get())
            self.manager.update_config('rename_script_path', self.script_path_var.get())
            self.manager.update_config('menu_text', self.menu_text_var.get())
            self.manager.update_config('icon_path', self.icon_path_var.get())
            self.manager.update_config('use_vbs', self.use_vbs_var.get())

            self.update_changes_status()
        except Exception as e:
            print(f"Lỗi khi theo dõi thay đổi: {e}")

    def update_changes_status(self):
        """Cập nhật trạng thái thay đổi"""
        if self.manager.has_unsaved_changes():
            self.changes_label.config(text="⚠ Có thay đổi chưa lưu", fg="orange")
        else:
            self.changes_label.config(text="✓ Tất cả thay đổi đã được lưu", fg="green")

    def save_config(self):
        """Lưu cấu hình"""
        self.manager.config['python_path'] = self.python_path_var.get()
        self.manager.config['rename_script_path'] = self.script_path_var.get()
        self.manager.config['menu_text'] = self.menu_text_var.get()
        self.manager.config['icon_path'] = self.icon_path_var.get()
        self.manager.config['use_vbs'] = self.use_vbs_var.get()

        self.manager.save_config()
        self.update_changes_status()
        messagebox.showinfo("Thành công", "Đã lưu cấu hình!")

    def auto_save(self):
        """Tự động lưu nếu có thay đổi"""
        if self.manager.auto_save_if_changed():
            self.update_changes_status()
            messagebox.showinfo("Tự động lưu", "Đã tự động lưu các thay đổi!")
        else:
            messagebox.showinfo("Tự động lưu", "Không có thay đổi nào cần lưu.")

    def save_changes(self):
        """Lưu tất cả thay đổi và chuẩn hóa đường dẫn"""
        # Cập nhật config từ GUI
        self.manager.config['python_path'] = self.python_path_var.get()
        self.manager.config['rename_script_path'] = self.script_path_var.get()
        self.manager.config['menu_text'] = self.menu_text_var.get()
        self.manager.config['icon_path'] = self.icon_path_var.get()
        self.manager.config['use_vbs'] = self.use_vbs_var.get()

        # Lưu với đường dẫn chuẩn hóa
        self.manager.save_config()

        # Cập nhật lại GUI với đường dẫn đã chuẩn hóa
        self.python_path_var.set(self.manager.config['python_path'])
        self.script_path_var.set(self.manager.config['rename_script_path'])
        self.icon_path_var.set(self.manager.config['icon_path'])

        self.update_changes_status()
        messagebox.showinfo("Thành công", "Đã lưu tất cả thay đổi và chuẩn hóa đường dẫn!")

    def register_menu_admin(self):
        """Đăng ký context menu với quyền admin"""
        # Lưu cấu hình trước khi đăng ký
        self.save_changes()

        success, message = self.manager.register_context_menu(force_admin=True)
        if success:
            messagebox.showinfo("Thành công", message)
        else:
            messagebox.showerror("Lỗi", message)

        self.check_status()

    def unregister_menu_admin(self):
        """Hủy đăng ký context menu với quyền admin"""
        if messagebox.askyesno("Xác nhận", "Bạn có chắc muốn hủy đăng ký context menu?"):
            success, message = self.manager.unregister_context_menu(force_admin=True)
            if success:
                messagebox.showinfo("Thành công", message)
            else:
                messagebox.showerror("Lỗi", message)

            self.check_status()

    def on_closing(self):
        """Xử lý khi đóng cửa sổ"""
        if self.manager.has_unsaved_changes():
            result = messagebox.askyesnocancel(
                "Thay đổi chưa lưu",
                "Bạn có thay đổi chưa lưu. Bạn có muốn lưu trước khi thoát không?"
            )
            if result is True:  # Yes - lưu và thoát
                self.save_config()
                self.root.destroy()
            elif result is False:  # No - thoát không lưu
                self.root.destroy()
            # Cancel - không làm gì cả
        else:
            self.root.destroy()
    
    def register_menu(self):
        """Đăng ký context menu"""
        # Lưu cấu hình trước khi đăng ký
        self.save_config()
        
        success, message = self.manager.register_context_menu()
        if success:
            messagebox.showinfo("Thành công", message)
        else:
            messagebox.showerror("Lỗi", message)
        
        self.check_status()
    
    def unregister_menu(self):
        """Hủy đăng ký context menu"""
        if messagebox.askyesno("Xác nhận", "Bạn có chắc muốn hủy đăng ký context menu?"):
            success, message = self.manager.unregister_context_menu()
            if success:
                messagebox.showinfo("Thành công", message)
            else:
                messagebox.showerror("Lỗi", message)
            
            self.check_status()
    
    def check_status(self):
        """Kiểm tra trạng thái đăng ký"""
        if self.manager.is_registered():
            self.status_label.config(text="✓ Context menu đã được đăng ký", fg="green")
        else:
            self.status_label.config(text="✗ Context menu chưa được đăng ký", fg="red")
    
    def run(self):
        """Chạy GUI"""
        self.root.mainloop()

def main():
    """Hàm main để chạy từ command line hoặc GUI"""
    if len(sys.argv) > 1:
        # Chạy từ command line
        manager = PDFContextMenuManager()

        if sys.argv[1] == "register":
            success, message = manager.register_context_menu()
            print(message)
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "register_admin":
            success, message = manager.register_context_menu(force_admin=False)
            print(message)
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "unregister":
            success, message = manager.unregister_context_menu()
            print(message)
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "unregister_admin":
            success, message = manager.unregister_context_menu(force_admin=False)
            print(message)
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "status":
            if manager.is_registered():
                print("Context menu đã được đăng ký")
            else:
                print("Context menu chưa được đăng ký")
            sys.exit(0)
        elif sys.argv[1] == "admin_status":
            if manager.is_admin():
                print("Đang chạy với quyền Administrator")
            else:
                print("Không có quyền Administrator")
            sys.exit(0)
    else:
        # Chạy GUI
        app = ContextMenuGUI()
        app.run()

if __name__ == "__main__":
    main()
