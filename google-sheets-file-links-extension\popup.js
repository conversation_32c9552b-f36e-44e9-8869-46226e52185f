// Popup JavaScript cho Google Sheets File Links Handler
console.log('Popup script loaded');

class PopupManager {
    constructor() {
        this.elements = {
            enabledToggle: document.getElementById('enabledToggle'),
            notificationsToggle: document.getElementById('notificationsToggle'),
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.getElementById('statusText'),
            refreshBtn: document.getElementById('refreshBtn'),
            testBtn: document.getElementById('testBtn'),
            status: document.getElementById('status')
        };
        
        this.init();
    }

    async init() {
        await this.loadSettings();
        this.setupEventListeners();
        this.updateStatus();
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['enabled', 'showNotifications']);
            
            this.elements.enabledToggle.checked = result.enabled !== false;
            this.elements.notificationsToggle.checked = result.showNotifications !== false;
            
        } catch (error) {
            console.error('Lỗi khi tải cài đặt:', error);
            this.showError('Không thể tải cài đặt');
        }
    }

    setupEventListeners() {
        // Toggle enabled/disabled
        this.elements.enabledToggle.addEventListener('change', async (e) => {
            try {
                await chrome.storage.sync.set({ enabled: e.target.checked });
                this.updateStatus();
                
                // Gửi message đến content script
                const tabs = await chrome.tabs.query({ 
                    url: ['https://docs.google.com/spreadsheets/*', 'https://sheets.google.com/*'] 
                });
                
                tabs.forEach(tab => {
                    chrome.tabs.sendMessage(tab.id, {
                        action: 'toggleEnabled',
                        enabled: e.target.checked
                    }).catch(() => {
                        // Ignore errors for tabs that don't have content script
                    });
                });
                
            } catch (error) {
                console.error('Lỗi khi thay đổi cài đặt:', error);
                this.showError('Không thể lưu cài đặt');
                e.target.checked = !e.target.checked; // Revert
            }
        });

        // Toggle notifications
        this.elements.notificationsToggle.addEventListener('change', async (e) => {
            try {
                await chrome.storage.sync.set({ showNotifications: e.target.checked });
            } catch (error) {
                console.error('Lỗi khi thay đổi cài đặt thông báo:', error);
                this.showError('Không thể lưu cài đặt thông báo');
                e.target.checked = !e.target.checked; // Revert
            }
        });

        // Refresh button
        this.elements.refreshBtn.addEventListener('click', async () => {
            this.elements.refreshBtn.disabled = true;
            this.elements.refreshBtn.textContent = 'Đang làm mới...';
            
            try {
                // Reload content scripts
                const tabs = await chrome.tabs.query({ 
                    url: ['https://docs.google.com/spreadsheets/*', 'https://sheets.google.com/*'] 
                });
                
                for (const tab of tabs) {
                    try {
                        await chrome.tabs.reload(tab.id);
                    } catch (error) {
                        console.log('Không thể reload tab:', tab.id);
                    }
                }
                
                this.showSuccess('Đã làm mới thành công');
                
            } catch (error) {
                console.error('Lỗi khi làm mới:', error);
                this.showError('Không thể làm mới');
            } finally {
                this.elements.refreshBtn.disabled = false;
                this.elements.refreshBtn.textContent = 'Làm mới';
            }
        });

        // Test button
        this.elements.testBtn.addEventListener('click', () => {
            this.runTest();
        });
    }

    async updateStatus() {
        try {
            const result = await chrome.storage.sync.get(['enabled']);
            const isEnabled = result.enabled !== false;
            
            if (isEnabled) {
                this.showSuccess('Extension đang hoạt động');
            } else {
                this.showWarning('Extension đã tắt');
            }
            
        } catch (error) {
            this.showError('Không thể kiểm tra trạng thái');
        }
    }

    async runTest() {
        this.elements.testBtn.disabled = true;
        this.elements.testBtn.textContent = 'Đang kiểm tra...';
        
        try {
            // Kiểm tra xem có tab Google Sheets nào đang mở không
            const tabs = await chrome.tabs.query({ 
                url: ['https://docs.google.com/spreadsheets/*', 'https://sheets.google.com/*'] 
            });
            
            if (tabs.length === 0) {
                this.showWarning('Không tìm thấy tab Google Sheets nào đang mở');
                return;
            }
            
            // Test với một file path mẫu
            const testFilePath = 'file:///C:/Windows/System32/notepad.exe';
            
            // Gửi test message đến background script
            const response = await chrome.runtime.sendMessage({
                action: 'openFile',
                filePath: testFilePath
            });
            
            if (response && response.success) {
                this.showSuccess('Test thành công! Extension hoạt động bình thường.');
            } else {
                this.showError('Test thất bại: ' + (response?.error || 'Lỗi không xác định'));
            }
            
        } catch (error) {
            console.error('Lỗi khi test:', error);
            this.showError('Lỗi khi chạy test: ' + error.message);
        } finally {
            this.elements.testBtn.disabled = false;
            this.elements.testBtn.textContent = 'Kiểm tra';
        }
    }

    showSuccess(message) {
        this.elements.status.className = 'status';
        this.elements.statusIndicator.style.background = '#34a853';
        this.elements.statusText.textContent = message;
    }

    showError(message) {
        this.elements.status.className = 'status error';
        this.elements.statusIndicator.style.background = '#ea4335';
        this.elements.statusText.textContent = message;
    }

    showWarning(message) {
        this.elements.status.className = 'status';
        this.elements.statusIndicator.style.background = '#fbbc04';
        this.elements.statusText.textContent = message;
    }
}

// Khởi tạo popup manager khi DOM loaded
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
