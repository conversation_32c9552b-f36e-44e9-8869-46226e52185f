# Hướng dẫn Cài đặt Chi tiết - Google Sheets File Links Handler

## 📋 Chuẩn bị

### Y<PERSON><PERSON> cầu hệ thống:
- **Trình duyệt:** Google Chrome phiên bản 88 trở lên
- **Hệ điều hành:** Windows 10/11 (đã test), macOS, Linux
- **Quyền:** Quyền cài đặt extension (không cần quyền admin)

## 🔧 Bước 1: Tạo Icons

1. **Mở file tạo icons:**
   - Mở file `create_icons.html` trong trình duyệt
   - Trang sẽ tự động tạo các icon cần thiết

2. **Tải xuống icons:**
   - Nhấp "Tải xuống tất cả Icons" hoặc tải từng icon
   - Tạo thư mục `icons` trong thư mục extension
   - Lưu các file với tên: `icon16.png`, `icon32.png`, `icon48.png`, `icon128.png`

## 🚀 Bước 2: Cài đặt Extension

### Phương pháp 1: Developer Mode (Khuyến nghị)

1. **Mở Chrome Extensions:**
   ```
   Cách 1: Gõ chrome://extensions/ vào thanh địa chỉ
   Cách 2: Menu ⋮ → More tools → Extensions
   Cách 3: Phím tắt Ctrl+Shift+Delete → Extensions
   ```

2. **Bật Developer Mode:**
   - Tìm toggle "Developer mode" ở góc trên bên phải
   - Bật nó lên (màu xanh)

3. **Load Extension:**
   - Nhấp nút "Load unpacked"
   - Chọn thư mục `google-sheets-file-links-extension`
   - Extension sẽ xuất hiện trong danh sách

4. **Kiểm tra cài đặt:**
   - Extension hiển thị với tên "Google Sheets File Links Handler"
   - Icon xuất hiện trên thanh công cụ Chrome
   - Trạng thái "Enabled"

### Phương pháp 2: Đóng gói thành .crx (Nâng cao)

1. **Tạo file .crx:**
   ```bash
   # Trong Chrome Extensions, nhấp "Pack extension"
   # Chọn thư mục extension
   # Tạo private key (tùy chọn)
   # Nhấp "Pack Extension"
   ```

2. **Cài đặt .crx:**
   - Kéo file .crx vào trang chrome://extensions/
   - Xác nhận cài đặt
   - Chấp nhận quyền

## ⚙️ Bước 3: Cấu hình Extension

1. **Mở popup cấu hình:**
   - Nhấp icon extension trên thanh công cụ
   - Popup sẽ hiển thị các tùy chọn

2. **Kiểm tra cài đặt:**
   - ✅ "Kích hoạt extension" - Bật
   - ✅ "Hiển thị thông báo" - Bật (tùy chọn)
   - Trạng thái: "Extension đang hoạt động"

3. **Test chức năng:**
   - Nhấp nút "Kiểm tra" trong popup
   - Nếu thành công: "Test thành công! Extension hoạt động bình thường"

## 🧪 Bước 4: Test Extension

### Test cơ bản:

1. **Mở Google Sheets:**
   - Truy cập https://sheets.google.com
   - Tạo spreadsheet mới hoặc mở có sẵn

2. **Nhập đường dẫn test:**
   ```
   file:///C:/Windows/System32/notepad.exe
   file:///C:/Windows/System32/calc.exe
   ```

3. **Kiểm tra chuyển đổi:**
   - Đường dẫn sẽ chuyển thành link màu xanh
   - Hover để xem tooltip
   - Nhấp để mở file

### Test nâng cao:

1. **Test với file thực:**
   ```
   file:///C:/Users/<USER>/Documents/test.pdf
   file:///D:/Projects/data.xlsx
   ```

2. **Test trong các cell khác nhau:**
   - Cell thường
   - Cell có formula
   - Cell merged

## 🔍 Troubleshooting

### Lỗi thường gặp:

#### 1. Extension không load được
```
Lỗi: "Could not load extension"
Giải pháp:
- Kiểm tra file manifest.json có lỗi syntax không
- Đảm bảo tất cả file cần thiết có trong thư mục
- Thử reload extension
```

#### 2. Icons không hiển thị
```
Lỗi: Icon mặc định hoặc không có icon
Giải pháp:
- Tạo thư mục icons/
- Tải xuống đầy đủ 4 file icon
- Đặt tên đúng: icon16.png, icon32.png, icon48.png, icon128.png
- Reload extension
```

#### 3. Extension không hoạt động trên Google Sheets
```
Lỗi: Không chuyển đổi file links
Giải pháp:
- Kiểm tra quyền trong manifest.json
- Refresh trang Google Sheets
- Kiểm tra Console (F12) có lỗi không
- Thử disable/enable extension
```

#### 4. File không mở được
```
Lỗi: Nhấp link nhưng file không mở
Giải pháp:
- Kiểm tra đường dẫn file có đúng không
- Đảm bảo file tồn tại
- Kiểm tra quyền truy cập file
- Thử mở file thủ công
```

### Debug Extension:

1. **Debug Content Script:**
   ```
   - Mở Google Sheets
   - Nhấn F12 → Console
   - Tìm log "Google Sheets File Links Handler - Content script loaded"
   ```

2. **Debug Background Script:**
   ```
   - Vào chrome://extensions/
   - Tìm extension → "Inspect views: background page"
   - Kiểm tra Console
   ```

3. **Debug Popup:**
   ```
   - Right-click icon extension
   - Chọn "Inspect popup"
   - Kiểm tra Console và Elements
   ```

## 📱 Sử dụng trên Mobile

Extension chỉ hoạt động trên Chrome desktop. Để sử dụng trên mobile:

1. **Chrome Mobile:** Không hỗ trợ extensions
2. **Kiwi Browser:** Có thể cài extension (Android)
3. **Yandex Browser:** Hỗ trợ một số extension (Android)

## 🔄 Cập nhật Extension

### Cập nhật thủ công:
1. Tải version mới
2. Thay thế file cũ
3. Vào chrome://extensions/
4. Nhấp nút reload (🔄) trên extension

### Cập nhật tự động:
- Chỉ có khi publish lên Chrome Web Store
- Developer mode không tự động cập nhật

## 🗑️ Gỡ cài đặt

1. **Vào chrome://extensions/**
2. **Tìm extension**
3. **Nhấp "Remove"**
4. **Xác nhận gỡ cài đặt**

Hoặc:
- Right-click icon → "Remove from Chrome"

## 📞 Hỗ trợ

Nếu gặp vấn đề:

1. **Kiểm tra lại các bước cài đặt**
2. **Xem phần Troubleshooting**
3. **Kiểm tra Console log**
4. **Tạo issue trên GitHub**

### Thông tin cần cung cấp khi báo lỗi:
- Phiên bản Chrome
- Hệ điều hành
- Mô tả lỗi chi tiết
- Screenshot (nếu có)
- Console log (nếu có)

## ✅ Checklist Cài đặt

- [ ] Tải xuống source code
- [ ] Tạo icons (4 file)
- [ ] Bật Developer mode
- [ ] Load unpacked extension
- [ ] Kiểm tra icon hiển thị
- [ ] Test popup hoạt động
- [ ] Test trên Google Sheets
- [ ] Kiểm tra file mở được

Chúc bạn sử dụng extension thành công! 🎉
