@echo off
setlocal

REM --- <PERSON><PERSON><PERSON> hình ---
set HOST_NAME=com.my_company.gsheets_file_opener

REM Đường dẫn cài đặt ứng dụng gốc
set TARGET_DIR=%APPDATA%\%HOST_NAME%

REM --- B<PERSON><PERSON> đầu gỡ cài đặt ---
echo Dang go cai dat Native Host cho %HOST_NAME%...

REM 1. Xóa khóa registry
set REG_KEY_PATH="HKCU\Software\Google\Chrome\NativeMessagingHosts\%HOST_NAME%"
echo Xoa khoa registry: %REG_KEY_PATH%
reg delete %REG_KEY_PATH% /f
if errorlevel 1 (
    echo Canh bao: Khong the xoa khoa registry. Co the no khong ton tai hoac ban khong co quyen.
) else (
    echo Da xoa khoa registry thanh cong.
)

REM 2. Xóa thư mục cài đặt
if exist "%TARGET_DIR%" (
    echo Xoa thu muc cai dat: %TARGET_DIR%
    rd /s /q "%TARGET_DIR%"
    if errorlevel 1 (
        echo Loi: Khong the xoa thu muc %TARGET_DIR%. Ban co the can xoa no thu cong.
    ) else (
        echo Da xoa thu muc cai dat thanh cong.
    )
) else (
    echo Thong tin: Thu muc %TARGET_DIR% khong ton tai.
)

echo.
echo --- HOAN TAT ---
echo Go cai dat Native Host hoan tat.
echo Vui long khoi dong lai Google Chrome de thay doi co hieu luc.
echo.

endlocal
pause
