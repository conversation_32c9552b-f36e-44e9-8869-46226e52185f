# Hướng dẫn Cài đặt Google Drive PDF Downloader

## Cài đặt Extension

### Phương pháp 1: Cài đặt thủ công (Developer Mode)

1. **Tải xuống extension**
   - Tải xuống hoặc clone repository này về máy
   - <PERSON><PERSON><PERSON><PERSON> nén (nếu tải file zip)

2. **Mở Chrome Extensions**
   - Mở trình duyệt Chrome
   - Truy cập `chrome://extensions/` hoặc:
     - Menu Chrome → More tools → Extensions
     - Hoặc nhấn `Ctrl+Shift+E` (Windows/Linux) / `Cmd+Shift+E` (Mac)

3. **Bật Developer Mode**
   - Ở góc trên bên phải, bật toggle "Developer mode"
   
4. **Load Extension**
   - Nhấp nút "Load unpacked"
   - Chọn thư mục `google-drive-pdf-downloader`
   - Extension sẽ xuất hiện trong danh sách

5. **Kiểm tra cài đặt**
   - Icon extension sẽ xuất hiện trên thanh công cụ Chrome
   - Nếu không thấy, nhấp vào icon puzzle để pin extension

### Phương pháp 2: Từ Chrome Web Store (Sắp có)
- Extension sẽ sớm có mặt trên Chrome Web Store
- Tìm kiếm "Google Drive PDF Downloader"
- Nhấp "Add to Chrome"

## Kiểm tra Extension hoạt động

1. **Mở file PDF trên Google Drive**
   - Truy cập Google Drive
   - Mở bất kỳ file PDF nào (URL dạng: `https://drive.google.com/file/d/FILE_ID/view`)

2. **Kiểm tra nút tải xuống**
   - Nút "Tải PDF" sẽ xuất hiện ở góc trên bên phải trang
   - Hoặc nhấp icon extension để mở popup

3. **Test tải xuống**
   - Nhấp nút "Tải PDF" hoặc "Tải xuống PDF" trong popup
   - File sẽ được tải xuống vào thư mục Downloads

## Khắc phục sự cố

### Extension không xuất hiện
- Kiểm tra Developer mode đã bật chưa
- Reload extension: nhấp icon reload ở extension
- Restart Chrome

### Nút không xuất hiện trên trang
- Đảm bảo đang ở trang Google Drive với URL đúng
- Reload trang (F5)
- Kiểm tra Console (F12) xem có lỗi không

### Không tải được file
- Kiểm tra quyền truy cập file
- Đảm bảo file là PDF hoặc có thể export PDF
- Thử với file khác

### Lỗi permissions
- Đảm bảo extension có đủ quyền:
  - Active Tab
  - Downloads
  - Storage
  - Host permissions cho Google Drive

## Gỡ cài đặt

1. Truy cập `chrome://extensions/`
2. Tìm "Google Drive PDF Downloader"
3. Nhấp "Remove"
4. Xác nhận gỡ cài đặt

## Cập nhật Extension

### Cập nhật thủ công
1. Tải phiên bản mới
2. Gỡ phiên bản cũ
3. Cài đặt phiên bản mới theo hướng dẫn trên

### Cập nhật tự động (khi có trên Chrome Web Store)
- Extension sẽ tự động cập nhật
- Hoặc nhấp "Update" trong chrome://extensions/

## Yêu cầu hệ thống

- **Trình duyệt**: Chrome 88+ hoặc Edge 88+
- **Hệ điều hành**: Windows, macOS, Linux
- **Kết nối internet**: Cần thiết để truy cập Google Drive

## Quyền riêng tư

Extension chỉ:
- Hoạt động trên trang Google Drive
- Không thu thập dữ liệu cá nhân
- Không gửi thông tin về server bên ngoài
- Chỉ truy cập file khi người dùng yêu cầu tải xuống

## Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra Console (F12) để xem lỗi
2. Thử với file PDF khác
3. Restart Chrome
4. Gỡ và cài lại extension
5. Báo cáo lỗi kèm thông tin chi tiết
