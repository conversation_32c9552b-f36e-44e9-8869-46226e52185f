#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test GUI để kiểm tra các tính năng mới
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox

def test_gui():
    """Test GUI với các tính năng mới"""
    
    # Import và chạy GUI
    try:
        from register_pdf_context_menu import ContextMenuGUI
        
        print("Khởi tạo GUI với các tính năng mới...")
        app = ContextMenuGUI()
        
        # Kiểm tra các thuộc tính mới
        checks = []
        
        # Kiểm tra manager có các phương thức mới
        if hasattr(app.manager, 'normalize_path'):
            checks.append("✅ normalize_path method")
        else:
            checks.append("❌ normalize_path method")
            
        if hasattr(app.manager, 'is_admin'):
            checks.append("✅ is_admin method")
        else:
            checks.append("❌ is_admin method")
            
        if hasattr(app.manager, 'run_as_admin'):
            checks.append("✅ run_as_admin method")
        else:
            checks.append("❌ run_as_admin method")
            
        # Kiểm tra GUI có các phương thức mới
        if hasattr(app, 'save_changes'):
            checks.append("✅ save_changes method")
        else:
            checks.append("❌ save_changes method")
            
        if hasattr(app, 'register_menu_admin'):
            checks.append("✅ register_menu_admin method")
        else:
            checks.append("❌ register_menu_admin method")
            
        if hasattr(app, 'unregister_menu_admin'):
            checks.append("✅ unregister_menu_admin method")
        else:
            checks.append("❌ unregister_menu_admin method")
        
        # In kết quả kiểm tra
        print("\nKiểm tra các tính năng:")
        for check in checks:
            print(check)
        
        # Test normalize_path
        if hasattr(app.manager, 'normalize_path'):
            test_path = "G:/My Drive/test/file.py"
            normalized = app.manager.normalize_path(test_path)
            print(f"\nTest normalize_path:")
            print(f"Input:  {test_path}")
            print(f"Output: {normalized}")
        
        # Test admin status
        if hasattr(app.manager, 'is_admin'):
            admin_status = app.manager.is_admin()
            print(f"\nAdmin status: {admin_status}")
        
        print("\nChạy GUI...")
        app.run()
        
    except Exception as e:
        print(f"Lỗi khi chạy GUI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gui()
