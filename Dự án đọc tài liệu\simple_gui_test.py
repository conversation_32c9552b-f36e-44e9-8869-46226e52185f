#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple GUI test để kiểm tra các tính năng mới
"""

import tkinter as tk
from tkinter import messagebox
import os
import sys

# Import manager từ file gốc
try:
    from register_pdf_context_menu import PDFContextMenuManager
    print("✅ Import PDFContextMenuManager thành công")
except Exception as e:
    print(f"❌ Lỗi import: {e}")
    sys.exit(1)

class SimpleGUI:
    def __init__(self):
        self.manager = PDFContextMenuManager()
        
        self.root = tk.Tk()
        self.root.title("🔧 PDF Context Menu Manager - Demo Tính năng mới")
        self.root.geometry("800x700")
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title = tk.Label(main_frame, text="🚀 Demo Tính năng mới", 
                        font=("Arial", 16, "bold"), fg="blue")
        title.pack(pady=(0, 20))
        
        # Admin status
        admin_status = "✅ Admin" if self.manager.is_admin() else "❌ Không có quyền Admin"
        admin_color = "green" if self.manager.is_admin() else "red"
        tk.Label(main_frame, text=f"Trạng thái quyền: {admin_status}", 
                fg=admin_color, font=("Arial", 12, "bold")).pack(pady=(0, 10))
        
        # Current config display
        config_frame = tk.LabelFrame(main_frame, text="📋 Cấu hình hiện tại", padx=10, pady=10)
        config_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Python path
        tk.Label(config_frame, text="🐍 Python Path:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        self.python_label = tk.Label(config_frame, text=self.manager.config.get('python_path', ''), 
                                    wraplength=700, justify=tk.LEFT, bg="lightyellow")
        self.python_label.pack(fill=tk.X, pady=(0, 10))
        
        # Script path
        tk.Label(config_frame, text="📄 Script Path:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        self.script_label = tk.Label(config_frame, text=self.manager.config.get('rename_script_path', ''), 
                                    wraplength=700, justify=tk.LEFT, bg="lightyellow")
        self.script_label.pack(fill=tk.X, pady=(0, 10))
        
        # Test normalize path
        test_frame = tk.LabelFrame(main_frame, text="🧪 Test Chuẩn hóa đường dẫn", padx=10, pady=10)
        test_frame.pack(fill=tk.X, pady=(0, 20))
        
        tk.Label(test_frame, text="Nhập đường dẫn để test:").pack(anchor=tk.W)
        self.test_path_var = tk.StringVar(value="G:/My Drive/test/file.py")
        self.test_entry = tk.Entry(test_frame, textvariable=self.test_path_var, width=80)
        self.test_entry.pack(fill=tk.X, pady=(5, 10))
        
        tk.Button(test_frame, text="🔄 Chuẩn hóa đường dẫn", 
                 command=self.test_normalize, bg="lightblue").pack()
        
        self.result_label = tk.Label(test_frame, text="", wraplength=700, justify=tk.LEFT, 
                                   bg="lightgreen", relief=tk.SUNKEN)
        self.result_label.pack(fill=tk.X, pady=(10, 0))
        
        # Action buttons
        button_frame = tk.LabelFrame(main_frame, text="🎛️ Hành động", padx=10, pady=10)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Row 1
        row1 = tk.Frame(button_frame)
        row1.pack(fill=tk.X, pady=(0, 10))
        
        tk.Button(row1, text="💾 Lưu cấu hình", command=self.save_config, 
                 bg="lightblue", width=20).pack(side=tk.LEFT, padx=(0, 10))
        tk.Button(row1, text="✨ Lưu và chuẩn hóa", command=self.save_and_normalize, 
                 bg="lightcyan", width=20).pack(side=tk.LEFT, padx=(0, 10))
        tk.Button(row1, text="🔍 Kiểm tra trạng thái", command=self.check_status, 
                 width=20).pack(side=tk.LEFT)
        
        # Row 2
        row2 = tk.Frame(button_frame)
        row2.pack(fill=tk.X)
        
        tk.Button(row2, text="✅ Đăng ký Context Menu (Admin)", command=self.register_admin, 
                 bg="lightgreen", width=30).pack(side=tk.LEFT, padx=(0, 10))
        tk.Button(row2, text="❌ Hủy đăng ký (Admin)", command=self.unregister_admin, 
                 bg="lightcoral", width=20).pack(side=tk.LEFT)
        
        # Status
        self.status_label = tk.Label(main_frame, text="", font=("Arial", 12, "bold"))
        self.status_label.pack(pady=(20, 0))
        
        # Info
        info_text = """🎯 DEMO TÍNH NĂNG MỚI:
• Chuẩn hóa đường dẫn: G:/path → G:\\\\path
• Quản lý quyền Admin tự động
• Lưu cấu hình với đường dẫn chuẩn hóa
• Đăng ký/hủy đăng ký context menu tự động"""
        
        tk.Label(main_frame, text=info_text, justify=tk.LEFT, 
                bg="lightyellow", relief=tk.RIDGE, padx=10, pady=10).pack(fill=tk.X, pady=(20, 0))
        
        # Initial status check
        self.check_status()
        
    def test_normalize(self):
        """Test chuẩn hóa đường dẫn"""
        input_path = self.test_path_var.get()
        if hasattr(self.manager, 'normalize_path'):
            normalized = self.manager.normalize_path(input_path)
            result_text = f"Input:  {input_path}\\nOutput: {normalized}"
            self.result_label.config(text=result_text)
        else:
            self.result_label.config(text="❌ Phương thức normalize_path không tồn tại")
    
    def save_config(self):
        """Lưu cấu hình"""
        self.manager.save_config()
        messagebox.showinfo("Thành công", "Đã lưu cấu hình!")
        self.update_display()
    
    def save_and_normalize(self):
        """Lưu và chuẩn hóa"""
        self.manager.save_config()
        self.update_display()
        
        paths_info = f"""Đã lưu và chuẩn hóa:

🐍 Python: {self.manager.config['python_path']}

📄 Script: {self.manager.config['rename_script_path']}

✅ Sử dụng dấu \\\\ như yêu cầu!"""
        
        messagebox.showinfo("Thành công", paths_info)
    
    def register_admin(self):
        """Đăng ký với admin"""
        if hasattr(self.manager, 'register_context_menu'):
            success, message = self.manager.register_context_menu(force_admin=True)
            if success:
                messagebox.showinfo("Thành công", f"✅ {message}")
            else:
                messagebox.showerror("Lỗi", f"❌ {message}")
            self.check_status()
        else:
            messagebox.showerror("Lỗi", "Phương thức register_context_menu không tồn tại")
    
    def unregister_admin(self):
        """Hủy đăng ký với admin"""
        if messagebox.askyesno("Xác nhận", "Hủy đăng ký context menu?"):
            if hasattr(self.manager, 'unregister_context_menu'):
                success, message = self.manager.unregister_context_menu(force_admin=True)
                if success:
                    messagebox.showinfo("Thành công", f"✅ {message}")
                else:
                    messagebox.showerror("Lỗi", f"❌ {message}")
                self.check_status()
            else:
                messagebox.showerror("Lỗi", "Phương thức unregister_context_menu không tồn tại")
    
    def check_status(self):
        """Kiểm tra trạng thái"""
        if hasattr(self.manager, 'is_registered'):
            if self.manager.is_registered():
                self.status_label.config(text="✅ Context menu đã được đăng ký", fg="green")
            else:
                self.status_label.config(text="❌ Context menu chưa được đăng ký", fg="red")
        else:
            self.status_label.config(text="❓ Không thể kiểm tra trạng thái", fg="orange")
    
    def update_display(self):
        """Cập nhật hiển thị"""
        self.python_label.config(text=self.manager.config.get('python_path', ''))
        self.script_label.config(text=self.manager.config.get('rename_script_path', ''))
    
    def run(self):
        """Chạy GUI"""
        self.root.mainloop()

def main():
    print("🚀 Khởi động Simple GUI Test...")
    try:
        app = SimpleGUI()
        app.run()
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
