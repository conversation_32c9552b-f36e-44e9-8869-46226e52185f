import sys
import os
import json
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import winreg
from pathlib import Path

class PythonVersionManager:
    def __init__(self):
        self.config_file = os.path.join(os.path.dirname(__file__), 'python_versions.json')
        self.python_versions = {}
        self.load_config()
        self.detect_python_versions()
        
    def load_config(self):
        """Load cấu hình phiên bản Python đã lưu"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.python_versions = json.load(f)
            except Exception as e:
                print(f"Lỗi khi đọc file config: {e}")
                self.python_versions = {}
        
    def save_config(self):
        """Lưu cấu hình phiên bản Python"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.python_versions, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"Lỗi khi lưu file config: {e}")
    
    def detect_python_versions(self):
        """Tự động phát hiện các phiên bản Python đã cài đặt"""
        detected_versions = {}
        
        # Kiểm tra Python trong PATH
        try:
            result = subprocess.run(['python', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip().replace('Python ', '')
                python_path = subprocess.run(['where', 'python'], capture_output=True, text=True).stdout.strip().split('\n')[0]
                detected_versions[f"Python {version} (PATH)"] = python_path
        except:
            pass
            
        # Kiểm tra python3 trong PATH
        try:
            result = subprocess.run(['python3', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip().replace('Python ', '')
                python_path = subprocess.run(['where', 'python3'], capture_output=True, text=True).stdout.strip().split('\n')[0]
                detected_versions[f"Python {version} (python3)"] = python_path
        except:
            pass
        
        # Kiểm tra trong Windows Registry
        try:
            self._detect_from_registry(detected_versions)
        except:
            pass
            
        # Kiểm tra các thư mục phổ biến
        common_paths = [
            r"C:\Python*",
            r"C:\Program Files\Python*",
            r"C:\Program Files (x86)\Python*",
            os.path.expanduser(r"~\AppData\Local\Programs\Python\Python*"),
            os.path.expanduser(r"~\AppData\Local\Microsoft\WindowsApps\python*.exe")
        ]
        
        for pattern in common_paths:
            try:
                import glob
                for path in glob.glob(pattern):
                    if os.path.isdir(path):
                        python_exe = os.path.join(path, 'python.exe')
                        if os.path.exists(python_exe):
                            try:
                                result = subprocess.run([python_exe, '--version'], capture_output=True, text=True)
                                if result.returncode == 0:
                                    version = result.stdout.strip().replace('Python ', '')
                                    detected_versions[f"Python {version} ({path})"] = python_exe
                            except:
                                pass
                    elif path.endswith('.exe') and os.path.exists(path):
                        try:
                            result = subprocess.run([path, '--version'], capture_output=True, text=True)
                            if result.returncode == 0:
                                version = result.stdout.strip().replace('Python ', '')
                                detected_versions[f"Python {version} (WindowsApps)"] = path
                        except:
                            pass
            except:
                pass
        
        # Cập nhật danh sách phiên bản
        for name, path in detected_versions.items():
            if name not in self.python_versions:
                self.python_versions[name] = {
                    'path': path,
                    'auto_detected': True
                }
        
        self.save_config()
    
    def _detect_from_registry(self, detected_versions):
        """Phát hiện Python từ Windows Registry"""
        try:
            # Kiểm tra HKEY_LOCAL_MACHINE
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Python\PythonCore") as key:
                i = 0
                while True:
                    try:
                        version = winreg.EnumKey(key, i)
                        with winreg.OpenKey(key, f"{version}\\InstallPath") as install_key:
                            install_path = winreg.QueryValue(install_key, "")
                            python_exe = os.path.join(install_path, 'python.exe')
                            if os.path.exists(python_exe):
                                detected_versions[f"Python {version} (Registry)"] = python_exe
                        i += 1
                    except WindowsError:
                        break
        except:
            pass
            
        # Kiểm tra HKEY_CURRENT_USER
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"SOFTWARE\Python\PythonCore") as key:
                i = 0
                while True:
                    try:
                        version = winreg.EnumKey(key, i)
                        with winreg.OpenKey(key, f"{version}\\InstallPath") as install_key:
                            install_path = winreg.QueryValue(install_key, "")
                            python_exe = os.path.join(install_path, 'python.exe')
                            if os.path.exists(python_exe):
                                detected_versions[f"Python {version} (User Registry)"] = python_exe
                        i += 1
                    except WindowsError:
                        break
        except:
            pass
    
    def add_python_version(self, name, path):
        """Thêm phiên bản Python mới"""
        if os.path.exists(path):
            try:
                result = subprocess.run([path, '--version'], capture_output=True, text=True)
                if result.returncode == 0:
                    self.python_versions[name] = {
                        'path': path,
                        'auto_detected': False
                    }
                    self.save_config()
                    return True, f"Đã thêm {name}"
                else:
                    return False, "File không phải là Python executable hợp lệ"
            except Exception as e:
                return False, f"Lỗi khi kiểm tra Python: {e}"
        else:
            return False, "File không tồn tại"
    
    def remove_python_version(self, name):
        """Xóa phiên bản Python"""
        if name in self.python_versions:
            del self.python_versions[name]
            self.save_config()
            return True
        return False
    
    def get_python_versions(self):
        """Lấy danh sách tất cả phiên bản Python"""
        return self.python_versions
    
    def get_python_path(self, name):
        """Lấy đường dẫn của phiên bản Python"""
        return self.python_versions.get(name, {}).get('path', '')
    
    def run_script_with_python(self, python_name, script_path, args=None):
        """Chạy script với phiên bản Python được chọn"""
        python_path = self.get_python_path(python_name)
        if not python_path:
            return False, f"Không tìm thấy phiên bản Python: {python_name}"
        
        if not os.path.exists(script_path):
            return False, f"Script không tồn tại: {script_path}"
        
        try:
            cmd = [python_path, script_path]
            if args:
                cmd.extend(args)
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return True, f"Exit code: {result.returncode}\nOutput: {result.stdout}\nError: {result.stderr}"
        except Exception as e:
            return False, f"Lỗi khi chạy script: {e}"

class PythonVersionGUI:
    def __init__(self):
        self.manager = PythonVersionManager()
        self.root = tk.Tk()
        self.root.title("Python Version Manager")
        self.root.geometry("800x600")
        self.setup_ui()
        
    def setup_ui(self):
        # Frame chính
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Cấu hình grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Danh sách phiên bản Python
        ttk.Label(main_frame, text="Phiên bản Python đã phát hiện:").grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        # Treeview để hiển thị danh sách
        columns = ('name', 'path', 'status')
        self.tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=10)
        self.tree.heading('name', text='Tên')
        self.tree.heading('path', text='Đường dẫn')
        self.tree.heading('status', text='Trạng thái')
        
        self.tree.column('name', width=200)
        self.tree.column('path', width=400)
        self.tree.column('status', width=100)
        
        self.tree.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Scrollbar cho treeview
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar.grid(row=1, column=3, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(button_frame, text="Làm mới", command=self.refresh_list).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Thêm Python", command=self.add_python).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Xóa", command=self.remove_python).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Chạy Script", command=self.run_script).pack(side=tk.LEFT, padx=(0, 5))
        
        # Khung chạy script
        script_frame = ttk.LabelFrame(main_frame, text="Chạy Script", padding="10")
        script_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(20, 0))
        script_frame.columnconfigure(1, weight=1)
        
        ttk.Label(script_frame, text="Script:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.script_path = ttk.Entry(script_frame)
        self.script_path.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(script_frame, text="Browse", command=self.browse_script).grid(row=0, column=2)
        
        ttk.Label(script_frame, text="Arguments:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.script_args = ttk.Entry(script_frame)
        self.script_args.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # Load danh sách ban đầu
        self.refresh_list()
        
    def refresh_list(self):
        """Làm mới danh sách phiên bản Python"""
        # Xóa tất cả items hiện tại
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Phát hiện lại các phiên bản
        self.manager.detect_python_versions()
        
        # Thêm vào treeview
        for name, info in self.manager.get_python_versions().items():
            status = "Tự động" if info.get('auto_detected', False) else "Thủ công"
            self.tree.insert('', tk.END, values=(name, info['path'], status))
    
    def add_python(self):
        """Thêm phiên bản Python mới"""
        file_path = filedialog.askopenfilename(
            title="Chọn Python executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        
        if file_path:
            name = f"Python Custom ({os.path.basename(os.path.dirname(file_path))})"
            success, message = self.manager.add_python_version(name, file_path)
            
            if success:
                messagebox.showinfo("Thành công", message)
                self.refresh_list()
            else:
                messagebox.showerror("Lỗi", message)
    
    def remove_python(self):
        """Xóa phiên bản Python được chọn"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một phiên bản Python để xóa")
            return
        
        item = self.tree.item(selected[0])
        name = item['values'][0]
        
        if messagebox.askyesno("Xác nhận", f"Bạn có chắc muốn xóa {name}?"):
            if self.manager.remove_python_version(name):
                messagebox.showinfo("Thành công", f"Đã xóa {name}")
                self.refresh_list()
            else:
                messagebox.showerror("Lỗi", f"Không thể xóa {name}")
    
    def browse_script(self):
        """Chọn file script để chạy"""
        file_path = filedialog.askopenfilename(
            title="Chọn Python script",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")]
        )
        
        if file_path:
            self.script_path.delete(0, tk.END)
            self.script_path.insert(0, file_path)
    
    def run_script(self):
        """Chạy script với phiên bản Python được chọn"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một phiên bản Python")
            return
        
        script_path = self.script_path.get().strip()
        if not script_path:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn script để chạy")
            return
        
        item = self.tree.item(selected[0])
        python_name = item['values'][0]
        
        args = self.script_args.get().strip().split() if self.script_args.get().strip() else None
        
        success, message = self.manager.run_script_with_python(python_name, script_path, args)
        
        if success:
            messagebox.showinfo("Kết quả", message)
        else:
            messagebox.showerror("Lỗi", message)
    
    def run(self):
        """Chạy GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    app = PythonVersionGUI()
    app.run()
