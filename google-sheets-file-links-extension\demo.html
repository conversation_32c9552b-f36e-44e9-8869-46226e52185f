<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Google Sheets File Links Handler</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4285f4, #34a853);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px dashed #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .demo-section h2 {
            color: #333;
            margin-top: 0;
            font-size: 1.5em;
            border-bottom: 2px solid #4285f4;
            padding-bottom: 10px;
        }
        
        .file-links {
            background: white;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #ddd;
            margin: 15px 0;
        }
        
        .file-link {
            display: block;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #4285f4;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
        }
        
        .instructions {
            background: #e8f0fe;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            color: #1a73e8;
            margin-top: 0;
        }
        
        .step {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #34a853;
        }
        
        .step-number {
            background: #34a853;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 10px 0;
        }
        
        .button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        
        .button:hover {
            background: #3367d6;
        }
        
        .button.secondary {
            background: #6c757d;
        }
        
        .button.secondary:hover {
            background: #545b62;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }
        
        .card h3 {
            margin-top: 0;
            color: #333;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Google Sheets File Links Handler</h1>
            <p>Demo và Hướng dẫn Sử dụng Extension</p>
        </div>
        
        <div class="content">
            <div class="demo-section">
                <h2>📋 Ví dụ File Links</h2>
                <p>Dưới đây là các ví dụ về đường dẫn file:/// mà extension sẽ xử lý:</p>
                
                <div class="file-links">
                    <h3>Windows Files:</h3>
                    <div class="file-link">file:///C:/Windows/System32/notepad.exe</div>
                    <div class="file-link">file:///C:/Windows/System32/calc.exe</div>
                    <div class="file-link">file:///C:/Windows/System32/mspaint.exe</div>
                    
                    <h3>Documents:</h3>
                    <div class="file-link">file:///C:/Users/<USER>/Documents/report.pdf</div>
                    <div class="file-link">file:///D:/Projects/data.xlsx</div>
                    <div class="file-link">file:///C:/Users/<USER>/Desktop/presentation.pptx</div>
                    
                    <h3>Media Files:</h3>
                    <div class="file-link">file:///C:/Users/<USER>/Pictures/photo.jpg</div>
                    <div class="file-link">file:///D:/Videos/tutorial.mp4</div>
                    <div class="file-link">file:///C:/Music/song.mp3</div>
                </div>
                
                <div class="warning">
                    <strong>⚠️ Lưu ý:</strong> Thay "YourName" bằng tên user thực tế của bạn và đảm bảo các file này tồn tại trên máy tính.
                </div>
            </div>
            
            <div class="demo-section">
                <h2>🚀 Hướng dẫn Test Extension</h2>
                
                <div class="instructions">
                    <h3>Các bước thực hiện:</h3>
                    
                    <div class="step">
                        <span class="step-number">1</span>
                        <strong>Cài đặt Extension:</strong> Làm theo hướng dẫn trong file INSTALLATION_GUIDE.md
                    </div>
                    
                    <div class="step">
                        <span class="step-number">2</span>
                        <strong>Mở Google Sheets:</strong> Truy cập https://sheets.google.com và tạo spreadsheet mới
                    </div>
                    
                    <div class="step">
                        <span class="step-number">3</span>
                        <strong>Copy đường dẫn test:</strong> Copy một trong các đường dẫn ở trên vào cell
                    </div>
                    
                    <div class="step">
                        <span class="step-number">4</span>
                        <strong>Kiểm tra chuyển đổi:</strong> Đường dẫn sẽ chuyển thành link màu xanh có thể nhấp được
                    </div>
                    
                    <div class="step">
                        <span class="step-number">5</span>
                        <strong>Test mở file:</strong> Nhấp vào link để mở file
                    </div>
                </div>
                
                <div class="success">
                    <strong>✅ Thành công:</strong> Nếu file mở được, extension đã hoạt động đúng!
                </div>
            </div>
            
            <div class="demo-section">
                <h2>🔧 Tính năng Extension</h2>
                
                <div class="grid">
                    <div class="card">
                        <h3>🎯 Tự động phát hiện</h3>
                        <p>Extension tự động tìm và chuyển đổi các đường dẫn file:/// trong Google Sheets thành link có thể nhấp được.</p>
                    </div>
                    
                    <div class="card">
                        <h3>🎨 Styling đẹp</h3>
                        <p>Link được styling với màu xanh, hiệu ứng hover, và tooltip hiển thị đường dẫn đầy đủ.</p>
                    </div>
                    
                    <div class="card">
                        <h3>🚀 Mở file nhanh</h3>
                        <p>Nhấp vào link để mở file ngay lập tức bằng ứng dụng mặc định của hệ điều hành.</p>
                    </div>
                    
                    <div class="card">
                        <h3>⚙️ Cấu hình linh hoạt</h3>
                        <p>Popup cho phép bật/tắt extension, cấu hình thông báo, và kiểm tra trạng thái.</p>
                    </div>
                    
                    <div class="card">
                        <h3>📱 Responsive</h3>
                        <p>Giao diện tương thích với nhiều kích thước màn hình và hỗ trợ dark mode.</p>
                    </div>
                    
                    <div class="card">
                        <h3>🔔 Thông báo</h3>
                        <p>Hiển thị thông báo trạng thái khi mở file thành công hoặc gặp lỗi.</p>
                    </div>
                </div>
            </div>
            
            <div class="demo-section">
                <h2>📝 Định dạng đường dẫn</h2>
                
                <h3>✅ Định dạng đúng:</h3>
                <div class="code">
                    file:///C:/path/to/file.ext<br>
                    file:///D:/folder/subfolder/document.pdf<br>
                    file:///C:/Users/<USER>/Desktop/file.docx
                </div>
                
                <h3>❌ Định dạng sai:</h3>
                <div class="code">
                    C:\path\to\file.ext (thiếu file:///)<br>
                    file://C:/path/to/file.ext (thiếu một dấu /)<br>
                    file:///C:\path\to\file.ext (dùng \ thay vì /)
                </div>
                
                <div class="warning">
                    <strong>💡 Mẹo:</strong> Để lấy đường dẫn đúng định dạng, copy đường dẫn file từ File Explorer và thêm "file:///" vào đầu, đồng thời thay tất cả "\" thành "/".
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>Google Sheets File Links Handler Extension</strong></p>
            <p>Phát triển để giúp việc quản lý file trong Google Sheets trở nên dễ dàng hơn</p>
            
            <a href="#" class="button" onclick="window.open('https://sheets.google.com', '_blank')">
                Mở Google Sheets
            </a>
            <a href="#" class="button secondary" onclick="window.open('chrome://extensions/', '_blank')">
                Quản lý Extensions
            </a>
        </div>
    </div>
    
    <script>
        // Thêm một số tương tác đơn giản
        document.addEventListener('DOMContentLoaded', function() {
            // Highlight file links khi hover
            const fileLinks = document.querySelectorAll('.file-link');
            fileLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.background = '#e3f2fd';
                    this.style.cursor = 'pointer';
                });
                
                link.addEventListener('mouseleave', function() {
                    this.style.background = '#f8f9fa';
                });
                
                // Copy to clipboard khi click
                link.addEventListener('click', function() {
                    navigator.clipboard.writeText(this.textContent).then(() => {
                        const originalText = this.textContent;
                        this.textContent = '✅ Đã copy!';
                        this.style.background = '#d4edda';
                        
                        setTimeout(() => {
                            this.textContent = originalText;
                            this.style.background = '#f8f9fa';
                        }, 1500);
                    });
                });
            });
            
            // Smooth scroll cho các section
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                step.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>
