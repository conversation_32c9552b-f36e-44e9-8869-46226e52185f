/* CSS cho content script - styling cho các file links trong Google Sheets */

/* <PERSON>yl<PERSON> cho các file links đ<PERSON><PERSON><PERSON> x<PERSON> lý */
.file-link-handler {
    color: #1a73e8 !important;
    text-decoration: underline !important;
    cursor: pointer !important;
    background: rgba(26, 115, 232, 0.1) !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    font-family: inherit !important;
    font-size: inherit !important;
    transition: all 0.2s ease !important;
    border: 1px solid transparent !important;
}

.file-link-handler:hover {
    background: rgba(26, 115, 232, 0.2) !important;
    border-color: #1a73e8 !important;
    text-decoration: none !important;
}

.file-link-handler:active {
    background: rgba(26, 115, 232, 0.3) !important;
    transform: translateY(1px) !important;
}

/* Styling cho notification */
.file-link-notification {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 999999 !important;
    padding: 12px 20px !important;
    border-radius: 6px !important;
    font-family: 'Google Sans', <PERSON><PERSON>, <PERSON><PERSON>, sans-serif !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    animation: slideInRight 0.3s ease-out !important;
    max-width: 400px !important;
    word-wrap: break-word !important;
}

.file-link-notification.success {
    background: linear-gradient(135deg, #34a853, #137333) !important;
    border-left: 4px solid #0d652d !important;
}

.file-link-notification.error {
    background: linear-gradient(135deg, #ea4335, #d33b2c) !important;
    border-left: 4px solid #b52d20 !important;
}

.file-link-notification.info {
    background: linear-gradient(135deg, #4285f4, #1a73e8) !important;
    border-left: 4px solid #1557b0 !important;
}

/* Animation cho notification */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Styling đặc biệt cho các cell trong Google Sheets */
[role="gridcell"] .file-link-handler {
    display: inline-block !important;
    max-width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

/* Styling cho formula bar */
.formula-bar-input .file-link-handler {
    background: transparent !important;
    padding: 0 !important;
    border-radius: 0 !important;
    border: none !important;
}

.formula-bar-input .file-link-handler:hover {
    background: rgba(26, 115, 232, 0.1) !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
}

/* Tooltip cho file links */
.file-link-handler::before {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
    max-width: 300px;
    word-wrap: break-word;
    white-space: normal;
}

.file-link-handler:hover::before {
    opacity: 1;
}

/* Responsive design cho mobile */
@media (max-width: 768px) {
    .file-link-notification {
        top: 10px !important;
        right: 10px !important;
        left: 10px !important;
        max-width: none !important;
        font-size: 13px !important;
        padding: 10px 15px !important;
    }
    
    .file-link-handler {
        font-size: 12px !important;
        padding: 1px 3px !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .file-link-handler {
        color: #8ab4f8 !important;
        background: rgba(138, 180, 248, 0.1) !important;
    }
    
    .file-link-handler:hover {
        background: rgba(138, 180, 248, 0.2) !important;
        border-color: #8ab4f8 !important;
    }
    
    .file-link-handler::before {
        background: rgba(255, 255, 255, 0.9);
        color: #333;
    }
}

/* Accessibility improvements */
.file-link-handler:focus {
    outline: 2px solid #4285f4 !important;
    outline-offset: 2px !important;
}

/* Print styles */
@media print {
    .file-link-handler {
        color: #000 !important;
        background: none !important;
        text-decoration: underline !important;
    }
    
    .file-link-notification {
        display: none !important;
    }
}
