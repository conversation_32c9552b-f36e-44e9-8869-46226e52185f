#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PDF Context Menu Manager - <PERSON><PERSON><PERSON> bản mới với tất cả tính năng
"""

import os
import sys
import winreg
import tkinter as tk
from tkinter import messagebox, filedialog
import json
import subprocess
import ctypes

class PDFContextMenuManager:
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.config_file = os.path.join(self.script_dir, 'context_menu_config.json')
        self.config_changed = False  # Theo dõi thay đổi cấu hình
        self.load_config()

    def is_admin(self):
        """Kiểm tra xem có quyền admin không"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def run_as_admin(self, action):
        """Chạy lại script với quyền admin"""
        try:
            if action == "register":
                params = f'"{sys.executable}" "{__file__}" register_admin'
            elif action == "unregister":
                params = f'"{sys.executable}" "{__file__}" unregister_admin'
            else:
                return False, "Hành động không hợp lệ"

            result = ctypes.windll.shell32.ShellExecuteW(
                None, "runas", "cmd.exe", f"/c {params}", None, 1
            )

            if result > 32:  # Success
                return True, f"Đã yêu cầu quyền admin để {action}"
            else:
                return False, "Người dùng từ chối cấp quyền admin"

        except Exception as e:
            return False, f"Lỗi khi yêu cầu quyền admin: {str(e)}"

    def normalize_path(self, path):
        """Chuẩn hóa đường dẫn sử dụng \\\\ thay vì /"""
        if path:
            # Chuyển đổi tất cả / thành \\\\
            normalized = path.replace('/', '\\\\')
            # Đảm bảo đường dẫn tuyệt đối
            if not os.path.isabs(normalized):
                normalized = os.path.abspath(normalized)
            return normalized.replace('/', '\\\\')
        return path

    def load_config(self):
        """Load cấu hình context menu"""
        # Sử dụng pythonw.exe để ẩn terminal
        python_exe = sys.executable
        if python_exe.endswith('python.exe'):
            pythonw_exe = python_exe.replace('python.exe', 'pythonw.exe')
            if os.path.exists(pythonw_exe):
                python_exe = pythonw_exe

        # Chuẩn hóa đường dẫn với \\\\
        python_exe = self.normalize_path(python_exe)
        rename_script_path = self.normalize_path(os.path.join(self.script_dir, 'rename_pdf.py'))
        vbs_script_path = self.normalize_path(os.path.join(self.script_dir, 'run_rename_pdf_hidden.vbs'))

        default_config = {
            'python_path': python_exe,
            'rename_script_path': rename_script_path,
            'vbs_script_path': vbs_script_path,
            'menu_text': 'Đổi tên PDF và lưu Google Sheet',
            'icon_path': '',
            'use_vbs': True  # Sử dụng VBS để ẩn terminal
        }

        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
            except Exception as e:
                print(f"Lỗi khi đọc config: {e}")

        self.config = default_config
        self.save_config()

    def save_config(self):
        """Lưu cấu hình context menu với đường dẫn chuẩn hóa"""
        try:
            # Chuẩn hóa tất cả đường dẫn trước khi lưu
            config_to_save = self.config.copy()

            # Chuẩn hóa các đường dẫn
            if 'python_path' in config_to_save:
                config_to_save['python_path'] = self.normalize_path(config_to_save['python_path'])
            if 'rename_script_path' in config_to_save:
                config_to_save['rename_script_path'] = self.normalize_path(config_to_save['rename_script_path'])
            if 'vbs_script_path' in config_to_save:
                config_to_save['vbs_script_path'] = self.normalize_path(config_to_save['vbs_script_path'])
            if 'icon_path' in config_to_save:
                config_to_save['icon_path'] = self.normalize_path(config_to_save['icon_path'])

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=4, ensure_ascii=False)

            # Cập nhật config hiện tại với đường dẫn đã chuẩn hóa
            self.config = config_to_save
            self.config_changed = False  # Reset flag sau khi lưu
            print(f"Đã lưu cấu hình vào: {self.config_file}")
        except Exception as e:
            print(f"Lỗi khi lưu config: {e}")

    def update_config(self, key, value):
        """Cập nhật cấu hình và đánh dấu có thay đổi"""
        # Chuẩn hóa đường dẫn nếu là path
        if key in ['python_path', 'rename_script_path', 'vbs_script_path', 'icon_path']:
            value = self.normalize_path(value)

        if self.config.get(key) != value:
            self.config[key] = value
            self.config_changed = True
            print(f"Cấu hình '{key}' đã được thay đổi")

    def has_unsaved_changes(self):
        """Kiểm tra có thay đổi chưa lưu không"""
        return self.config_changed

    def auto_save_if_changed(self):
        """Tự động lưu nếu có thay đổi"""
        if self.config_changed:
            self.save_config()
            return True
        return False

    def register_context_menu(self, force_admin=False):
        """Đăng ký context menu cho file PDF"""
        # Kiểm tra quyền admin
        if not self.is_admin():
            if force_admin:
                return self.run_as_admin("register")
            else:
                return False, "Cần quyền Administrator để đăng ký context menu."

        try:
            # Tạo key cho PDF files
            pdf_key_path = r"*\shell\RenamePDF"

            # Tạo key chính
            with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path) as key:
                winreg.SetValue(key, "", winreg.REG_SZ, self.config['menu_text'])

                # Thêm icon nếu có
                if self.config['icon_path'] and os.path.exists(self.config['icon_path']):
                    winreg.SetValueEx(key, "Icon", 0, winreg.REG_SZ, self.config['icon_path'])

            # Tạo command key
            command_key_path = pdf_key_path + r"\command"
            with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, command_key_path) as key:
                if self.config.get('use_vbs', False) and os.path.exists(self.config.get('vbs_script_path', '')):
                    # Sử dụng VBS để ẩn terminal
                    command = f'wscript.exe "{self.config["vbs_script_path"]}" "%1"'
                else:
                    # Sử dụng pythonw.exe để ẩn terminal
                    command = f'"{self.config["python_path"]}" "{self.config["rename_script_path"]}" "%1"'
                winreg.SetValue(key, "", winreg.REG_SZ, command)

            return True, "Đã đăng ký context menu thành công!"

        except PermissionError:
            return False, "Cần quyền Administrator để đăng ký context menu."
        except Exception as e:
            return False, f"Lỗi khi đăng ký context menu: {str(e)}"

    def unregister_context_menu(self, force_admin=False):
        """Hủy đăng ký context menu"""
        # Kiểm tra quyền admin
        if not self.is_admin():
            if force_admin:
                return self.run_as_admin("unregister")
            else:
                return False, "Cần quyền Administrator để hủy đăng ký context menu."

        try:
            pdf_key_path = r"*\shell\RenamePDF"
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path + r"\command")
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path)
            return True, "Đã hủy đăng ký context menu thành công!"
        except FileNotFoundError:
            return False, "Context menu chưa được đăng ký."
        except PermissionError:
            return False, "Cần quyền Administrator để hủy đăng ký context menu."
        except Exception as e:
            return False, f"Lỗi khi hủy đăng ký context menu: {str(e)}"

    def is_registered(self):
        """Kiểm tra xem context menu đã được đăng ký chưa"""
        try:
            with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF"):
                return True
        except FileNotFoundError:
            return False
        except Exception:
            return False

class ContextMenuGUI:
    def __init__(self):
        self.manager = PDFContextMenuManager()
        self.root = tk.Tk()
        self.root.title("PDF Context Menu Manager - Phiên bản Enhanced")

        # Tính toán kích thước màn hình và đặt kích thước cửa sổ
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Đặt kích thước cửa sổ lớn hơn để hiển thị đầy đủ
        window_width = min(900, int(screen_width * 0.8))
        window_height = min(800, int(screen_height * 0.8))

        # Tính toán vị trí giữa màn hình
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.resizable(True, True)  # Cho phép thay đổi kích thước
        self.root.minsize(800, 700)  # Kích thước tối thiểu

        # Theo dõi thay đổi
        self.setup_change_tracking()
        self.setup_ui()

        # Xử lý khi đóng cửa sổ
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_ui(self):
        # Tạo canvas và scrollbar cho scrollable content
        canvas = tk.Canvas(self.root)
        scrollbar = tk.Scrollbar(self.root, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas và scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Thêm mouse wheel scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        canvas.bind("<MouseWheel>", _on_mousewheel)

        # Lưu reference để sử dụng sau
        self.canvas = canvas
        self.scrollable_frame = scrollable_frame

        # Frame chính trong scrollable area
        main_frame = tk.Frame(scrollable_frame, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Tiêu đề
        title_label = tk.Label(main_frame, text="🔧 Quản lý Context Menu cho PDF - Phiên bản Enhanced",
                              font=("Arial", 16, "bold"), fg="blue")
        title_label.pack(pady=(0, 20))

        # Thêm nút mở rộng/thu gọn
        expand_frame = tk.Frame(main_frame)
        expand_frame.pack(fill=tk.X, pady=(0, 15))

        self.expand_button = tk.Button(expand_frame, text="📏 Mở rộng cửa sổ",
                                      command=self.toggle_window_size, bg="lightgray")
        self.expand_button.pack(side=tk.RIGHT)

        tk.Label(expand_frame, text="💡 Tip: Bạn có thể kéo để thay đổi kích thước cửa sổ",
                font=("Arial", 9), fg="gray").pack(side=tk.LEFT)

        # Trạng thái hiện tại và admin
        status_frame = tk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(0, 15))

        # Trạng thái đăng ký
        tk.Label(status_frame, text="Trạng thái đăng ký:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        self.status_label = tk.Label(status_frame, text="", fg="blue")
        self.status_label.pack(anchor=tk.W)

        # Trạng thái admin
        admin_status = "✅ Đang chạy với quyền Administrator" if self.manager.is_admin() else "❌ Không có quyền Administrator"
        admin_color = "green" if self.manager.is_admin() else "red"
        tk.Label(status_frame, text="Trạng thái quyền:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(5, 0))
        tk.Label(status_frame, text=admin_status, fg=admin_color).pack(anchor=tk.W)

        # Trạng thái thay đổi
        self.changes_frame = tk.LabelFrame(main_frame, text="📝 Trạng thái thay đổi", padx=10, pady=5)
        self.changes_frame.pack(fill=tk.X, pady=(0, 15))

        self.changes_label = tk.Label(self.changes_frame, text="", fg="orange")
        self.changes_label.pack(anchor=tk.W)

        # Cấu hình
        config_frame = tk.LabelFrame(main_frame, text="⚙️ Cấu hình", padx=10, pady=10)
        config_frame.pack(fill=tk.X, pady=(0, 15))

        # Python path
        python_frame = tk.Frame(config_frame)
        python_frame.pack(fill=tk.X, pady=(0, 8))
        tk.Label(python_frame, text="🐍 Đường dẫn Python:", font=("Arial", 9, "bold")).pack(anchor=tk.W)
        python_entry_frame = tk.Frame(python_frame)
        python_entry_frame.pack(fill=tk.X, pady=(3, 0))
        self.python_path_var = tk.StringVar(value=self.manager.config['python_path'])
        self.python_path_var.trace_add('write', self.on_config_change)
        self.python_path_entry = tk.Entry(python_entry_frame, textvariable=self.python_path_var, font=("Consolas", 8))
        self.python_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(python_entry_frame, text="📁 Browse",
                 command=self.browse_python, bg="lightblue").pack(side=tk.RIGHT, padx=(5, 0))

        # Script path
        script_frame = tk.Frame(config_frame)
        script_frame.pack(fill=tk.X, pady=(0, 8))
        tk.Label(script_frame, text="📄 Đường dẫn Script:", font=("Arial", 9, "bold")).pack(anchor=tk.W)
        script_entry_frame = tk.Frame(script_frame)
        script_entry_frame.pack(fill=tk.X, pady=(3, 0))
        self.script_path_var = tk.StringVar(value=self.manager.config['rename_script_path'])
        self.script_path_var.trace_add('write', self.on_config_change)
        self.script_path_entry = tk.Entry(script_entry_frame, textvariable=self.script_path_var, font=("Consolas", 8))
        self.script_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(script_entry_frame, text="📁 Browse",
                 command=self.browse_script, bg="lightblue").pack(side=tk.RIGHT, padx=(5, 0))

        # Menu text
        menu_frame = tk.Frame(config_frame)
        menu_frame.pack(fill=tk.X, pady=(0, 8))
        tk.Label(menu_frame, text="📝 Tên hiển thị trong menu:", font=("Arial", 9, "bold")).pack(anchor=tk.W)
        self.menu_text_var = tk.StringVar(value=self.manager.config['menu_text'])
        self.menu_text_var.trace_add('write', self.on_config_change)
        self.menu_text_entry = tk.Entry(menu_frame, textvariable=self.menu_text_var)
        self.menu_text_entry.pack(fill=tk.X, pady=(3, 0))

        # Icon path (optional)
        icon_frame = tk.Frame(config_frame)
        icon_frame.pack(fill=tk.X, pady=(0, 8))
        tk.Label(icon_frame, text="🎨 Đường dẫn Icon (tùy chọn):", font=("Arial", 9, "bold")).pack(anchor=tk.W)
        icon_entry_frame = tk.Frame(icon_frame)
        icon_entry_frame.pack(fill=tk.X, pady=(3, 0))
        self.icon_path_var = tk.StringVar(value=self.manager.config.get('icon_path', ''))
        self.icon_path_var.trace_add('write', self.on_config_change)
        self.icon_path_entry = tk.Entry(icon_entry_frame, textvariable=self.icon_path_var, font=("Consolas", 8))
        self.icon_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(icon_entry_frame, text="📁 Browse",
                 command=self.browse_icon, bg="lightblue").pack(side=tk.RIGHT, padx=(5, 0))

        # Hide terminal option
        hide_frame = tk.Frame(config_frame)
        hide_frame.pack(fill=tk.X, pady=(0, 5))
        self.use_vbs_var = tk.BooleanVar(value=self.manager.config.get('use_vbs', True))
        self.use_vbs_var.trace_add('write', self.on_config_change)
        self.use_vbs_checkbox = tk.Checkbutton(hide_frame,
                                              text="🔇 Ẩn cửa sổ terminal khi chạy (khuyến nghị)",
                                              variable=self.use_vbs_var)
        self.use_vbs_checkbox.pack(anchor=tk.W)

        # Buttons - Chia thành nhiều hàng với layout rõ ràng
        button_frame = tk.LabelFrame(main_frame, text="🎛️ Điều khiển", padx=15, pady=15)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        # Hàng 1: Các nút lưu cấu hình
        save_section = tk.LabelFrame(button_frame, text="💾 Quản lý cấu hình", padx=10, pady=10)
        save_section.pack(fill=tk.X, pady=(0, 15))

        save_buttons = tk.Frame(save_section)
        save_buttons.pack(fill=tk.X)

        tk.Button(save_buttons, text="💾 Lưu cấu hình cơ bản",
                 command=self.save_config, bg="lightblue", height=2, width=20).pack(side=tk.LEFT, padx=(0, 10), pady=5)
        tk.Button(save_buttons, text="🔄 Tự động lưu",
                 command=self.auto_save, bg="lightyellow", height=2, width=15).pack(side=tk.LEFT, padx=(0, 10), pady=5)
        tk.Button(save_buttons, text="✨ Lưu và chuẩn hóa đường dẫn",
                 command=self.save_changes, bg="lightcyan", height=2, width=25).pack(side=tk.LEFT, padx=(0, 10), pady=5)

        # Hàng 2: Kiểm tra trạng thái
        status_section = tk.LabelFrame(button_frame, text="🔍 Kiểm tra trạng thái", padx=10, pady=10)
        status_section.pack(fill=tk.X, pady=(0, 15))

        status_buttons = tk.Frame(status_section)
        status_buttons.pack(fill=tk.X)

        tk.Button(status_buttons, text="🔍 Kiểm tra trạng thái đăng ký",
                 command=self.check_status, height=2, width=25).pack(side=tk.LEFT, padx=(0, 10), pady=5)
        tk.Button(status_buttons, text="🔧 Test chuẩn hóa đường dẫn",
                 command=self.test_normalize_path, bg="lightgray", height=2, width=25).pack(side=tk.LEFT, pady=5)

        # Hàng 3: Các nút context menu (cần admin)
        admin_section = tk.LabelFrame(button_frame, text="🔐 Quản lý Context Menu (cần quyền Admin)", padx=10, pady=10)
        admin_section.pack(fill=tk.X, pady=(0, 10))

        admin_buttons = tk.Frame(admin_section)
        admin_buttons.pack(fill=tk.X)

        tk.Button(admin_buttons, text="✅ Đăng ký Context Menu (Admin)",
                 command=self.register_menu_admin, bg="lightgreen", height=2, width=30).pack(side=tk.LEFT, padx=(0, 15), pady=5)
        tk.Button(admin_buttons, text="❌ Hủy đăng ký Context Menu (Admin)",
                 command=self.unregister_menu_admin, bg="lightcoral", height=2, width=30).pack(side=tk.LEFT, pady=5)

        # Hướng dẫn chi tiết
        help_frame = tk.LabelFrame(main_frame, text="📖 Hướng dẫn sử dụng", padx=15, pady=15)
        help_frame.pack(fill=tk.X, pady=(20, 0))

        # Tính năng mới
        features_frame = tk.LabelFrame(help_frame, text="🚀 Tính năng mới", padx=10, pady=10)
        features_frame.pack(fill=tk.X, pady=(0, 15))

        features_text = """✨ Chuẩn hóa đường dẫn tự động: G:/path → G:\\\\path
🔐 Quản lý quyền Administrator tự động
📝 Theo dõi thay đổi real-time với thông báo
💾 Nhiều tùy chọn lưu cấu hình
🎛️ Giao diện mở rộng với scrollbar
🧪 Chức năng test chuẩn hóa đường dẫn"""

        tk.Label(features_frame, text=features_text, justify=tk.LEFT,
                font=("Arial", 9), bg="lightcyan").pack(fill=tk.X)

        # Hướng dẫn sử dụng
        usage_frame = tk.LabelFrame(help_frame, text="📋 Cách sử dụng", padx=10, pady=10)
        usage_frame.pack(fill=tk.X, pady=(0, 15))

        usage_text = """1️⃣ Cấu hình đường dẫn Python và script rename_pdf.py
2️⃣ Nhấn "✨ Lưu và chuẩn hóa đường dẫn" để áp dụng format \\\\
3️⃣ Nhấn "✅ Đăng ký Context Menu (Admin)" - tự động yêu cầu quyền admin
4️⃣ Chuột phải vào file PDF để sử dụng menu đã tạo
5️⃣ Sử dụng "🔧 Test chuẩn hóa đường dẫn" để kiểm tra tính năng"""

        tk.Label(usage_frame, text=usage_text, justify=tk.LEFT,
                font=("Arial", 9), bg="lightgreen").pack(fill=tk.X)

        # Lưu ý quan trọng
        note_frame = tk.LabelFrame(help_frame, text="⚠️ Lưu ý quan trọng", padx=10, pady=10)
        note_frame.pack(fill=tk.X)

        note_text = """🔹 Đường dẫn sẽ được tự động chuẩn hóa thành dạng: G:\\\\My Drive\\\\folder\\\\file.py
🔹 Các thao tác đăng ký/hủy đăng ký sẽ tự động yêu cầu quyền Administrator
🔹 Bạn có thể kéo để thay đổi kích thước cửa sổ hoặc dùng nút "Mở rộng"
🔹 Tất cả thay đổi sẽ được theo dõi và hiển thị trạng thái real-time"""

        tk.Label(note_frame, text=note_text, justify=tk.LEFT,
                font=("Arial", 9), bg="lightyellow").pack(fill=tk.X)

        # Kiểm tra trạng thái ban đầu
        self.check_status()
        self.update_changes_status()

    def setup_change_tracking(self):
        """Thiết lập theo dõi thay đổi"""
        pass

    def on_config_change(self, *args):
        """Callback khi có thay đổi cấu hình"""
        try:
            self.manager.update_config('python_path', self.python_path_var.get())
            self.manager.update_config('rename_script_path', self.script_path_var.get())
            self.manager.update_config('menu_text', self.menu_text_var.get())
            self.manager.update_config('icon_path', self.icon_path_var.get())
            self.manager.update_config('use_vbs', self.use_vbs_var.get())

            self.update_changes_status()
        except Exception as e:
            print(f"Lỗi khi theo dõi thay đổi: {e}")

    def update_changes_status(self):
        """Cập nhật trạng thái thay đổi"""
        if self.manager.has_unsaved_changes():
            self.changes_label.config(text="⚠️ Có thay đổi chưa lưu - Nhấn 'Lưu thay đổi' để chuẩn hóa đường dẫn", fg="orange")
        else:
            self.changes_label.config(text="✅ Tất cả thay đổi đã được lưu và chuẩn hóa", fg="green")

    def browse_python(self):
        """Chọn file Python executable"""
        file_path = filedialog.askopenfilename(
            title="Chọn Python executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if file_path:
            normalized_path = self.manager.normalize_path(file_path)
            self.python_path_var.set(normalized_path)
            messagebox.showinfo("Đường dẫn đã chuẩn hóa", f"Đường dẫn đã được chuẩn hóa:\\n{normalized_path}")

    def browse_script(self):
        """Chọn file script Python"""
        file_path = filedialog.askopenfilename(
            title="Chọn rename_pdf.py",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")]
        )
        if file_path:
            normalized_path = self.manager.normalize_path(file_path)
            self.script_path_var.set(normalized_path)
            messagebox.showinfo("Đường dẫn đã chuẩn hóa", f"Đường dẫn đã được chuẩn hóa:\\n{normalized_path}")

    def browse_icon(self):
        """Chọn file icon"""
        file_path = filedialog.askopenfilename(
            title="Chọn file icon",
            filetypes=[("Icon files", "*.ico"), ("Image files", "*.png;*.jpg;*.bmp"), ("All files", "*.*")]
        )
        if file_path:
            normalized_path = self.manager.normalize_path(file_path)
            self.icon_path_var.set(normalized_path)
            messagebox.showinfo("Đường dẫn đã chuẩn hóa", f"Đường dẫn đã được chuẩn hóa:\\n{normalized_path}")

    def save_config(self):
        """Lưu cấu hình cơ bản"""
        self.manager.config['python_path'] = self.python_path_var.get()
        self.manager.config['rename_script_path'] = self.script_path_var.get()
        self.manager.config['menu_text'] = self.menu_text_var.get()
        self.manager.config['icon_path'] = self.icon_path_var.get()
        self.manager.config['use_vbs'] = self.use_vbs_var.get()

        self.manager.save_config()
        self.update_changes_status()
        messagebox.showinfo("Thành công", "Đã lưu cấu hình!")

    def auto_save(self):
        """Tự động lưu nếu có thay đổi"""
        if self.manager.auto_save_if_changed():
            self.update_changes_status()
            messagebox.showinfo("Tự động lưu", "Đã tự động lưu các thay đổi!")
        else:
            messagebox.showinfo("Tự động lưu", "Không có thay đổi nào cần lưu.")

    def save_changes(self):
        """Lưu tất cả thay đổi và chuẩn hóa đường dẫn"""
        # Cập nhật config từ GUI
        self.manager.config['python_path'] = self.python_path_var.get()
        self.manager.config['rename_script_path'] = self.script_path_var.get()
        self.manager.config['menu_text'] = self.menu_text_var.get()
        self.manager.config['icon_path'] = self.icon_path_var.get()
        self.manager.config['use_vbs'] = self.use_vbs_var.get()

        # Lưu với đường dẫn chuẩn hóa
        self.manager.save_config()

        # Cập nhật lại GUI với đường dẫn đã chuẩn hóa
        self.python_path_var.set(self.manager.config['python_path'])
        self.script_path_var.set(self.manager.config['rename_script_path'])
        self.icon_path_var.set(self.manager.config['icon_path'])

        self.update_changes_status()

        # Hiển thị thông báo chi tiết
        normalized_paths = f"""Đã lưu và chuẩn hóa tất cả đường dẫn:

🐍 Python: {self.manager.config['python_path']}

📄 Script: {self.manager.config['rename_script_path']}

🎨 Icon: {self.manager.config['icon_path'] or '(không có)'}

✅ Tất cả đường dẫn đã sử dụng dấu \\\\ như yêu cầu!"""

        messagebox.showinfo("Thành công", normalized_paths)

    def toggle_window_size(self):
        """Chuyển đổi kích thước cửa sổ"""
        current_geometry = self.root.geometry()
        current_width = int(current_geometry.split('x')[0])

        if current_width < 1000:
            # Mở rộng
            self.root.geometry("1200x900")
            self.expand_button.config(text="📐 Thu nhỏ cửa sổ")
        else:
            # Thu nhỏ
            self.root.geometry("900x800")
            self.expand_button.config(text="📏 Mở rộng cửa sổ")

    def test_normalize_path(self):
        """Test chuẩn hóa đường dẫn"""
        from tkinter import simpledialog

        test_path = simpledialog.askstring(
            "Test chuẩn hóa đường dẫn",
            "Nhập đường dẫn để test chuẩn hóa:",
            initialvalue="G:/My Drive/test/file.py"
        )

        if test_path:
            normalized = self.manager.normalize_path(test_path)
            result_msg = f"""🧪 KẾT QUẢ TEST CHUẨN HÓA ĐƯỜNG DẪN:

📥 Input:  {test_path}
📤 Output: {normalized}

✅ Đường dẫn đã được chuẩn hóa sử dụng dấu \\\\ thay vì /"""

            messagebox.showinfo("Kết quả test", result_msg)

    def register_menu_admin(self):
        """Đăng ký context menu với quyền admin"""
        # Lưu cấu hình trước khi đăng ký
        self.save_changes()

        success, message = self.manager.register_context_menu(force_admin=True)
        if success:
            messagebox.showinfo("Thành công", f"✅ {message}")
        else:
            messagebox.showerror("Lỗi", f"❌ {message}")

        self.check_status()

    def unregister_menu_admin(self):
        """Hủy đăng ký context menu với quyền admin"""
        if messagebox.askyesno("Xác nhận", "Bạn có chắc muốn hủy đăng ký context menu?"):
            success, message = self.manager.unregister_context_menu(force_admin=True)
            if success:
                messagebox.showinfo("Thành công", f"✅ {message}")
            else:
                messagebox.showerror("Lỗi", f"❌ {message}")

            self.check_status()

    def check_status(self):
        """Kiểm tra trạng thái đăng ký"""
        if self.manager.is_registered():
            self.status_label.config(text="✅ Context menu đã được đăng ký", fg="green")
        else:
            self.status_label.config(text="❌ Context menu chưa được đăng ký", fg="red")

    def on_closing(self):
        """Xử lý khi đóng cửa sổ"""
        if self.manager.has_unsaved_changes():
            result = messagebox.askyesnocancel(
                "Thay đổi chưa lưu",
                "Bạn có thay đổi chưa lưu. Bạn có muốn lưu trước khi thoát không?"
            )
            if result is True:  # Yes - lưu và thoát
                self.save_changes()
                self.root.destroy()
            elif result is False:  # No - thoát không lưu
                self.root.destroy()
            # Cancel - không làm gì cả
        else:
            self.root.destroy()

    def run(self):
        """Chạy GUI"""
        self.root.mainloop()

def main():
    """Hàm main"""
    if len(sys.argv) > 1:
        # Command line mode
        manager = PDFContextMenuManager()

        if sys.argv[1] == "register_admin":
            success, message = manager.register_context_menu(force_admin=False)
            print(message)
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "unregister_admin":
            success, message = manager.unregister_context_menu(force_admin=False)
            print(message)
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "admin_status":
            if manager.is_admin():
                print("Đang chạy với quyền Administrator")
            else:
                print("Không có quyền Administrator")
            sys.exit(0)
    else:
        # GUI mode
        print("🚀 Khởi động PDF Context Menu Manager - Phiên bản Enhanced")
        print("✨ Tính năng mới: Chuẩn hóa đường dẫn tự động với \\\\")
        print("🔐 Tính năng mới: Quản lý quyền Administrator tự động")
        app = ContextMenuGUI()
        app.run()

if __name__ == "__main__":
    main()