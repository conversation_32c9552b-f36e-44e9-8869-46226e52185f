import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
import time
from datetime import datetime
import sys
import os

class ProgressDialog:
    def __init__(self, title="Đang xử lý...", parent=None):
        # Tạo root window mới
        self.root = tk.Tk()
        self.root.title(title)
        self.root.geometry("600x400")
        self.root.resizable(False, False)

        # Set window properties
        self.root.attributes('-topmost', True)  # Always on top

        # Hide the window initially
        self.root.withdraw()
        
        # Variables
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar()
        self.detail_var = tk.StringVar()
        self.is_cancelled = False
        self.is_completed = False
        
        self.setup_ui()
        self.center_window()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Đang xử lý PDF...", 
                               font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(main_frame, 
                                           variable=self.progress_var,
                                           maximum=100,
                                           length=400,
                                           mode='determinate')
        self.progress_bar.pack(pady=(0, 10))
        
        # Progress percentage
        self.progress_label = ttk.Label(main_frame, text="0%", 
                                       font=("Arial", 10, "bold"))
        self.progress_label.pack()
        
        # Status
        status_frame = ttk.LabelFrame(main_frame, text="Trạng thái", padding="10")
        status_frame.pack(fill=tk.X, pady=(20, 10))
        
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var,
                                     wraplength=450)
        self.status_label.pack(anchor=tk.W)
        
        # Detail
        detail_frame = ttk.LabelFrame(main_frame, text="Chi tiết", padding="10")
        detail_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Scrolled text for details
        self.detail_text = scrolledtext.ScrolledText(detail_frame,
                                                    height=8,
                                                    wrap=tk.WORD,
                                                    font=("Consolas", 9))
        self.detail_text.pack(fill=tk.BOTH, expand=True)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        self.cancel_button = ttk.Button(button_frame, text="Hủy", 
                                       command=self.cancel)
        self.cancel_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        self.close_button = ttk.Button(button_frame, text="Đóng", 
                                      command=self.close,
                                      state=tk.DISABLED)
        self.close_button.pack(side=tk.RIGHT)
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.cancel)
        
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def update_progress(self, percentage, status="", detail=""):
        """Update progress bar and status"""
        def update():
            self.progress_var.set(percentage)
            self.progress_label.config(text=f"{percentage:.1f}%")
            
            if status:
                self.status_var.set(status)
                
            if detail:
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.detail_text.insert(tk.END, f"[{timestamp}] {detail}\n")
                self.detail_text.see(tk.END)
                
            self.root.update()
            
        if threading.current_thread() == threading.main_thread():
            update()
        else:
            self.root.after(0, update)
            
    def set_completed(self, success=True, message=""):
        """Mark as completed"""
        def complete():
            self.is_completed = True
            if success:
                self.progress_var.set(100)
                self.progress_label.config(text="100%")
                self.status_var.set(message or "Hoàn thành!")
                self.detail_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] ✅ Hoàn thành thành công!\n")
            else:
                self.status_var.set(message or "Có lỗi xảy ra!")
                self.detail_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] ❌ {message}\n")
                
            self.detail_text.see(tk.END)
            self.cancel_button.config(state=tk.DISABLED)
            self.close_button.config(state=tk.NORMAL)
            self.root.update()
            
        if threading.current_thread() == threading.main_thread():
            complete()
        else:
            self.root.after(0, complete)
            
    def cancel(self):
        """Cancel the operation"""
        self.is_cancelled = True
        self.detail_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️ Đã hủy bởi người dùng\n")
        self.detail_text.see(tk.END)
        self.close()
        
    def close(self):
        """Close the dialog"""
        try:
            self.root.destroy()
        except:
            pass
            
    def show(self):
        """Show the dialog"""
        self.center_window()
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()

    def hide(self):
        """Hide the dialog"""
        self.root.withdraw()

class ProgressManager:
    """Manager class để dễ dàng sử dụng progress dialog"""
    
    def __init__(self, title="Đang xử lý...", parent=None):
        self.dialog = ProgressDialog(title, parent)
        self.current_step = 0
        self.total_steps = 0
        self.step_weights = []
        
    def set_steps(self, steps):
        """Set danh sách các bước với trọng số
        steps: list of (step_name, weight) tuples
        """
        self.steps = steps
        self.total_steps = len(steps)
        self.step_weights = [weight for _, weight in steps]
        self.total_weight = sum(self.step_weights)
        self.current_step = 0
        
    def start_step(self, step_index, detail=""):
        """Bắt đầu một bước"""
        if step_index < len(self.steps):
            step_name, _ = self.steps[step_index]
            self.current_step = step_index
            
            # Calculate progress up to this step
            progress = sum(self.step_weights[:step_index]) / self.total_weight * 100
            
            self.dialog.update_progress(
                progress, 
                f"Bước {step_index + 1}/{self.total_steps}: {step_name}",
                detail or f"Bắt đầu {step_name}..."
            )
            
    def update_step_progress(self, step_progress, detail=""):
        """Update progress trong bước hiện tại
        step_progress: 0-100 (progress của bước hiện tại)
        """
        if self.current_step < len(self.steps):
            # Progress của các bước đã hoàn thành
            completed_progress = sum(self.step_weights[:self.current_step]) / self.total_weight * 100
            
            # Progress của bước hiện tại
            current_step_weight = self.step_weights[self.current_step]
            current_step_progress = (step_progress / 100) * (current_step_weight / self.total_weight * 100)
            
            total_progress = completed_progress + current_step_progress
            
            step_name, _ = self.steps[self.current_step]
            self.dialog.update_progress(
                total_progress,
                f"Bước {self.current_step + 1}/{self.total_steps}: {step_name} ({step_progress:.1f}%)",
                detail
            )
            
    def complete_step(self, detail=""):
        """Hoàn thành bước hiện tại"""
        if self.current_step < len(self.steps):
            step_name, _ = self.steps[self.current_step]
            
            # Progress sau khi hoàn thành bước này
            progress = sum(self.step_weights[:self.current_step + 1]) / self.total_weight * 100
            
            self.dialog.update_progress(
                progress,
                f"Hoàn thành: {step_name}",
                detail or f"✅ Hoàn thành {step_name}"
            )
            
    def set_completed(self, success=True, message=""):
        """Hoàn thành toàn bộ quá trình"""
        self.dialog.set_completed(success, message)
        
    def is_cancelled(self):
        """Kiểm tra xem có bị hủy không"""
        return self.dialog.is_cancelled
        
    def show(self):
        """Hiển thị dialog"""
        self.dialog.show()
        
    def close(self):
        """Đóng dialog"""
        self.dialog.close()

# Test function
def test_progress_dialog():
    """Test progress dialog"""
    import random
    
    def simulate_work():
        manager = ProgressManager("Test Progress Dialog")
        
        # Define steps
        steps = [
            ("Khởi tạo", 10),
            ("Chuyển đổi PDF", 30),
            ("OCR văn bản", 40),
            ("Trích xuất thông tin", 15),
            ("Lưu kết quả", 5)
        ]
        
        manager.set_steps(steps)
        manager.show()
        
        try:
            for i, (step_name, weight) in enumerate(steps):
                if manager.is_cancelled():
                    break
                    
                manager.start_step(i, f"Bắt đầu {step_name}...")
                
                # Simulate work with sub-progress
                for j in range(10):
                    if manager.is_cancelled():
                        break
                        
                    time.sleep(0.2)
                    sub_progress = (j + 1) * 10
                    manager.update_step_progress(
                        sub_progress, 
                        f"{step_name}: {sub_progress}% hoàn thành"
                    )
                
                manager.complete_step(f"Hoàn thành {step_name}")
                
            if not manager.is_cancelled():
                manager.set_completed(True, "Tất cả các bước đã hoàn thành!")
            else:
                manager.set_completed(False, "Đã bị hủy bởi người dùng")
                
        except Exception as e:
            manager.set_completed(False, f"Lỗi: {str(e)}")
            
        # Keep dialog open for a moment
        time.sleep(2)
        manager.close()
    
    # Run in thread
    thread = threading.Thread(target=simulate_work, daemon=True)
    thread.start()
    
    # Start tkinter mainloop
    root = tk.Tk()
    root.withdraw()  # Hide main window
    root.mainloop()

if __name__ == "__main__":
    test_progress_dialog()
