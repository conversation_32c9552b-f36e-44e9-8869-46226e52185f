2025-06-09 22:54:52 - rename_pdf - INFO - ============================================================
2025-06-09 22:54:52 - rename_pdf - INFO - KHỞI ĐỘNG RENAME_PDF.PYW (NO CONSOLE)
2025-06-09 22:54:52 - rename_pdf - INFO - Python version: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-06-09 22:54:52 - rename_pdf - INFO - Script path: E:\app\htdocs\Dự án đọc tài liệu\rename_pdf.pyw
2025-06-09 22:54:52 - rename_pdf - INFO - Working directory: E:\app\htdocs\Dự án đọc tài liệu
2025-06-09 22:54:52 - rename_pdf - INFO - Arguments: ['E:\\app\\htdocs\\Dự án đọc tài liệu\\rename_pdf.pyw', 'E:\\app\\htdocs\\Dự án đọc tài liệu\\20250530.330.TB-TTĐT THÔNG BÁO Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf']
2025-06-09 22:54:52 - rename_pdf - INFO - ============================================================
2025-06-09 22:54:52 - rename_pdf - INFO - PDF path từ argument: E:\app\htdocs\Dự án đọc tài liệu\20250530.330.TB-TTĐT THÔNG BÁO Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-09 22:54:52 - rename_pdf - INFO - File PDF hợp lệ, bắt đầu xử lý...
2025-06-09 22:54:52 - rename_pdf - INFO - Starting process_pdf wrapper for: E:\app\htdocs\Dự án đọc tài liệu\20250530.330.TB-TTĐT THÔNG BÁO Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-09 22:54:52 - rename_pdf - INFO - Progress manager created successfully
2025-06-09 22:54:52 - rename_pdf - INFO - Starting PDF processing...
2025-06-09 22:54:52 - rename_pdf - INFO - ============================================================
2025-06-09 22:54:52 - rename_pdf - INFO - BẮT ĐẦU XỬ LÝ PDF: E:\app\htdocs\Dự án đọc tài liệu\20250530.330.TB-TTĐT THÔNG BÁO Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-09 22:54:52 - rename_pdf - INFO - ============================================================
2025-06-09 22:54:52 - rename_pdf - INFO - File PDF: 20250530.330.TB-TTĐT THÔNG BÁO Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-09 22:54:52 - rename_pdf - INFO - Kích thước: 92,217 bytes (0.09 MB)
2025-06-09 22:54:52 - rename_pdf - INFO - Script directory: E:\app\htdocs\Dự án đọc tài liệu
2025-06-09 22:54:52 - rename_pdf - INFO - Config file: E:\app\htdocs\Dự án đọc tài liệu\config.json
2025-06-09 22:54:52 - rename_pdf - INFO - Kiểm tra kết nối các dịch vụ...
2025-06-09 22:54:56 - rename_pdf - DEBUG - Kết quả kiểm tra kết nối:
2025-06-09 22:54:56 - rename_pdf - DEBUG -   google_api: ✓ - Kết nối Google API thành công
2025-06-09 22:54:56 - rename_pdf - DEBUG -   google_vision: ✓ - Kết nối Google Vision API thành công
2025-06-09 22:54:56 - rename_pdf - DEBUG -   openai: ✓ - Kết nối OpenAI API thành công (v1.84.0, model: gpt-4o-mini)
2025-06-09 22:54:56 - rename_pdf - DEBUG -   grok: ✗ - Không thể kết nối với bất kỳ Grok model nào. Lỗi cuối: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}}
2025-06-09 22:54:56 - rename_pdf - DEBUG -   sheet: ✓ - Kết nối Google Sheet thành công: aaUntitled spreadsheet
2025-06-09 22:54:56 - rename_pdf - INFO - Tất cả kết nối đều thành công!
2025-06-09 22:54:56 - rename_pdf - INFO - Bắt đầu chuyển đổi PDF thành ảnh...
2025-06-09 22:54:57 - rename_pdf - INFO - Chuyển đổi PDF hoàn tất trong 0.95s - Số trang: 2
2025-06-09 22:54:57 - rename_pdf - INFO - Bắt đầu OCR với Google Vision...
2025-06-09 22:54:57 - rename_pdf - DEBUG - Credentials file: G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/Json/soy-tube-461903-r0-34d09c650c6b.json
2025-06-09 22:54:57 - rename_pdf - DEBUG - Vision client khởi tạo thành công
2025-06-09 22:54:57 - rename_pdf - DEBUG - OCR trang 1/2
2025-06-09 22:54:58 - rename_pdf - DEBUG - Trang 1 OCR hoàn tất trong 0.71s - 2158 ký tự
2025-06-09 22:54:58 - rename_pdf - DEBUG - OCR trang 2/2
2025-06-09 22:54:58 - rename_pdf - DEBUG - Trang 2 OCR hoàn tất trong 0.41s - 1372 ký tự
2025-06-09 22:54:58 - rename_pdf - INFO - OCR hoàn tất trong 1.16s - Tổng 3,531 ký tự
2025-06-09 22:54:58 - rename_pdf - DEBUG - Text preview: NGÂN HÀNG
CHÍNH SÁCH XÃ HỘI
TRUNG TÂM ĐÀO TẠO
Số: 330/TB-TTĐT
CỘNG HOÀ XÃ HỘI CHỦ NGHĨA VIỆT NAM
Độc lập - Tự do - Hạnh phúc
Hà Nội, ngày 30 tháng 05 năm 2025
THÔNG BÁO
Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo
tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp
Kính gửi: Giám đốc Chi nhánh NHCSXH các tỉnh, thành phố: Thái Bình;
Đắk Lắk; Huế và Hải Phòng
Thực hiện Kế hoạch tổ chức khóa đào tạo vận hành hệ thống Intellect phiên
bản nâng cấp đã được T...
2025-06-09 22:54:58 - rename_pdf - INFO - Khởi tạo AI client...
2025-06-09 22:54:58 - rename_pdf - INFO - Khởi tạo AI client - Provider: grok, Model: grok-1.5
2025-06-09 22:54:58 - rename_pdf - DEBUG - Grok API key length: 84
2025-06-09 22:54:58 - rename_pdf - INFO - Khởi tạo Grok client thành công (modern)
2025-06-09 22:54:58 - rename_pdf - INFO - Bắt đầu trích xuất thông tin từ ảnh trang đầu...
2025-06-09 22:54:58 - rename_pdf - DEBUG - First page image size: 215662 bytes
2025-06-09 22:54:58 - rename_pdf - DEBUG - Generating content with grok
2025-06-09 22:54:58 - rename_pdf - DEBUG - Using image data, size: 215662 bytes
2025-06-09 22:54:58 - rename_pdf - DEBUG - Calling Grok API...
2025-06-09 22:54:58 - rename_pdf - WARNING - Grok không hỗ trợ vision, sẽ sử dụng text fallback
2025-06-09 22:54:58 - rename_pdf - ERROR - Lỗi khi generate với AI (grok) sau 0.24s: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}}
2025-06-09 22:54:59 - rename_pdf - ERROR - Traceback (most recent call last):
  File "E:\app\htdocs\Dự án đọc tài liệu\rename_pdf.py", line 335, in generate_with_ai
    response = grok_client.chat.completions.create(
        model=model,
        messages=[{"role": "user", "content": prompt}],
        timeout=30
    )
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\groq\resources\chat\completions.py", line 361, in create
    return self._post(
           ~~~~~~~~~~^
        "/openai/v1/chat/completions",
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<39 lines>...
        stream_cls=Stream[ChatCompletionChunk],
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\groq\_base_client.py", line 1222, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\groq\_base_client.py", line 1031, in request
    raise self._make_status_error_from_response(err.response) from None
groq.AuthenticationError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}}

2025-06-09 22:54:59 - rename_pdf - ERROR - Lỗi khi trích xuất thông tin từ ảnh: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}}
2025-06-09 22:54:59 - rename_pdf - ERROR - Traceback (most recent call last):
  File "E:\app\htdocs\Dự án đọc tài liệu\rename_pdf.py", line 620, in process_pdf_with_progress
    file_info = generate_with_ai(ai_client, extraction_prompt, provider, first_page_image_data)
  File "E:\app\htdocs\Dự án đọc tài liệu\rename_pdf.py", line 335, in generate_with_ai
    response = grok_client.chat.completions.create(
        model=model,
        messages=[{"role": "user", "content": prompt}],
        timeout=30
    )
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\groq\resources\chat\completions.py", line 361, in create
    return self._post(
           ~~~~~~~~~~^
        "/openai/v1/chat/completions",
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<39 lines>...
        stream_cls=Stream[ChatCompletionChunk],
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\groq\_base_client.py", line 1222, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\groq\_base_client.py", line 1031, in request
    raise self._make_status_error_from_response(err.response) from None
groq.AuthenticationError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}}

2025-06-09 22:54:59 - rename_pdf - INFO - Fallback: Sử dụng text từ OCR...
2025-06-09 22:54:59 - rename_pdf - DEBUG - First page text length: 2158
2025-06-09 22:54:59 - rename_pdf - DEBUG - Generating content with grok
2025-06-09 22:54:59 - rename_pdf - DEBUG - Prompt length: 2546
2025-06-09 22:54:59 - rename_pdf - DEBUG - Calling Grok API...
2025-06-09 22:54:59 - rename_pdf - ERROR - Lỗi khi generate với AI (grok) sau 0.18s: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}}
2025-06-09 22:54:59 - rename_pdf - ERROR - Traceback (most recent call last):
  File "E:\app\htdocs\Dự án đọc tài liệu\rename_pdf.py", line 341, in generate_with_ai
    response = grok_client.chat.completions.create(
        model=model,
        messages=[{"role": "user", "content": prompt}],
        timeout=30
    )
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\groq\resources\chat\completions.py", line 361, in create
    return self._post(
           ~~~~~~~~~~^
        "/openai/v1/chat/completions",
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<39 lines>...
        stream_cls=Stream[ChatCompletionChunk],
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\groq\_base_client.py", line 1222, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\groq\_base_client.py", line 1031, in request
    raise self._make_status_error_from_response(err.response) from None
groq.AuthenticationError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}}

2025-06-09 22:54:59 - rename_pdf - ERROR - Lỗi cả vision và text fallback: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}}
2025-06-09 22:54:59 - rename_pdf - INFO - PDF processing completed
2025-06-09 22:55:03 - rename_pdf - INFO - Progress dialog completed
2025-06-09 22:55:03 - rename_pdf - INFO - KẾT THÚC RENAME_PDF.PYW
2025-06-09 22:55:03 - rename_pdf - INFO - ============================================================
2025-06-09 22:56:59 - rename_pdf - INFO - ============================================================
2025-06-09 22:56:59 - rename_pdf - INFO - KHỞI ĐỘNG RENAME_PDF.PYW (NO CONSOLE)
2025-06-09 22:56:59 - rename_pdf - INFO - Python version: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-06-09 22:56:59 - rename_pdf - INFO - Script path: E:\app\htdocs\Dự án đọc tài liệu\rename_pdf.pyw
2025-06-09 22:56:59 - rename_pdf - INFO - Working directory: E:\app\htdocs\Dự án đọc tài liệu
2025-06-09 22:56:59 - rename_pdf - INFO - Arguments: ['E:\\app\\htdocs\\Dự án đọc tài liệu\\rename_pdf.pyw']
2025-06-09 22:56:59 - rename_pdf - INFO - ============================================================
2025-06-09 22:56:59 - rename_pdf - ERROR - Vui lòng cung cấp đường dẫn file PDF
2025-06-09 22:56:59 - rename_pdf - INFO - Cách sử dụng: python rename_pdf.pyw <path_to_pdf_file>
2025-06-09 22:56:59 - rename_pdf - INFO - Notification: Lỗi - Vui lòng cung cấp đường dẫn file PDF
2025-06-09 22:57:00 - rename_pdf - INFO - KẾT THÚC RENAME_PDF.PYW
2025-06-09 22:57:00 - rename_pdf - INFO - ============================================================
2025-06-09 22:57:48 - rename_pdf - INFO - ============================================================
2025-06-09 22:57:48 - rename_pdf - INFO - KHỞI ĐỘNG RENAME_PDF.PYW (NO CONSOLE)
2025-06-09 22:57:48 - rename_pdf - INFO - Python version: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-06-09 22:57:48 - rename_pdf - INFO - Script path: E:\app\htdocs\Dự án đọc tài liệu\rename_pdf.pyw
2025-06-09 22:57:48 - rename_pdf - INFO - Working directory: E:\app\htdocs\Dự án đọc tài liệu
2025-06-09 22:57:48 - rename_pdf - INFO - Arguments: ['E:\\app\\htdocs\\Dự án đọc tài liệu\\rename_pdf.pyw', 'E:\\app\\htdocs\\Dự án đọc tài liệu\\20250530.330.TB-TTĐT THÔNG BÁO Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf']
2025-06-09 22:57:48 - rename_pdf - INFO - ============================================================
2025-06-09 22:57:48 - rename_pdf - INFO - PDF path từ argument: E:\app\htdocs\Dự án đọc tài liệu\20250530.330.TB-TTĐT THÔNG BÁO Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-09 22:57:48 - rename_pdf - INFO - File PDF hợp lệ, bắt đầu xử lý...
2025-06-09 22:57:48 - rename_pdf - INFO - Starting process_pdf wrapper for: E:\app\htdocs\Dự án đọc tài liệu\20250530.330.TB-TTĐT THÔNG BÁO Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-09 22:57:48 - rename_pdf - INFO - Progress manager created successfully
2025-06-09 22:57:48 - rename_pdf - INFO - Starting PDF processing...
2025-06-09 22:57:48 - rename_pdf - INFO - ============================================================
2025-06-09 22:57:48 - rename_pdf - INFO - BẮT ĐẦU XỬ LÝ PDF: E:\app\htdocs\Dự án đọc tài liệu\20250530.330.TB-TTĐT THÔNG BÁO Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-09 22:57:48 - rename_pdf - INFO - ============================================================
2025-06-09 22:57:48 - rename_pdf - INFO - File PDF: 20250530.330.TB-TTĐT THÔNG BÁO Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-09 22:57:48 - rename_pdf - INFO - Kích thước: 92,217 bytes (0.09 MB)
2025-06-09 22:57:48 - rename_pdf - INFO - Script directory: E:\app\htdocs\Dự án đọc tài liệu
2025-06-09 22:57:48 - rename_pdf - INFO - Config file: E:\app\htdocs\Dự án đọc tài liệu\config.json
2025-06-09 22:57:48 - rename_pdf - INFO - Kiểm tra kết nối các dịch vụ...
2025-06-09 22:57:50 - rename_pdf - DEBUG - Kết quả kiểm tra kết nối:
2025-06-09 22:57:50 - rename_pdf - DEBUG -   google_api: ✓ - Kết nối Google API thành công
2025-06-09 22:57:50 - rename_pdf - DEBUG -   google_vision: ✓ - Kết nối Google Vision API thành công
2025-06-09 22:57:50 - rename_pdf - DEBUG -   openai: ✓ - Kết nối OpenAI API thành công (v1.84.0, model: gpt-4o-mini)
2025-06-09 22:57:50 - rename_pdf - DEBUG -   grok: ✗ - Không thể kết nối với bất kỳ Grok model nào. Lỗi cuối: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}}
2025-06-09 22:57:50 - rename_pdf - DEBUG -   sheet: ✓ - Kết nối Google Sheet thành công: aaUntitled spreadsheet
2025-06-09 22:57:50 - rename_pdf - INFO - Tất cả kết nối đều thành công!
2025-06-09 22:57:50 - rename_pdf - INFO - Bắt đầu chuyển đổi PDF thành ảnh...
2025-06-09 22:57:51 - rename_pdf - INFO - Chuyển đổi PDF hoàn tất trong 0.90s - Số trang: 2
2025-06-09 22:57:51 - rename_pdf - INFO - Bắt đầu OCR với Google Vision...
2025-06-09 22:57:51 - rename_pdf - DEBUG - Credentials file: G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/Json/soy-tube-461903-r0-34d09c650c6b.json
2025-06-09 22:57:51 - rename_pdf - DEBUG - Vision client khởi tạo thành công
2025-06-09 22:57:51 - rename_pdf - DEBUG - OCR trang 1/2
2025-06-09 22:57:52 - rename_pdf - DEBUG - Trang 1 OCR hoàn tất trong 0.75s - 2158 ký tự
2025-06-09 22:57:52 - rename_pdf - DEBUG - OCR trang 2/2
2025-06-09 22:57:53 - rename_pdf - DEBUG - Trang 2 OCR hoàn tất trong 0.50s - 1372 ký tự
2025-06-09 22:57:53 - rename_pdf - INFO - OCR hoàn tất trong 1.29s - Tổng 3,531 ký tự
2025-06-09 22:57:53 - rename_pdf - DEBUG - Text preview: NGÂN HÀNG
CHÍNH SÁCH XÃ HỘI
TRUNG TÂM ĐÀO TẠO
Số: 330/TB-TTĐT
CỘNG HOÀ XÃ HỘI CHỦ NGHĨA VIỆT NAM
Độc lập - Tự do - Hạnh phúc
Hà Nội, ngày 30 tháng 05 năm 2025
THÔNG BÁO
Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo
tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp
Kính gửi: Giám đốc Chi nhánh NHCSXH các tỉnh, thành phố: Thái Bình;
Đắk Lắk; Huế và Hải Phòng
Thực hiện Kế hoạch tổ chức khóa đào tạo vận hành hệ thống Intellect phiên
bản nâng cấp đã được T...
2025-06-09 22:57:53 - rename_pdf - INFO - Khởi tạo AI client...
2025-06-09 22:57:53 - rename_pdf - INFO - Khởi tạo AI client - Provider: openai, Model: gpt-4o-mini
2025-06-09 22:57:53 - rename_pdf - DEBUG - OpenAI API key length: 164
2025-06-09 22:57:53 - rename_pdf - INFO - OpenAI version detected: 1.84.0
2025-06-09 22:57:53 - rename_pdf - INFO - Khởi tạo OpenAI client thành công (modern v1.0+)
2025-06-09 22:57:53 - rename_pdf - INFO - Bắt đầu trích xuất thông tin từ ảnh trang đầu...
2025-06-09 22:57:53 - rename_pdf - DEBUG - First page image size: 215662 bytes
2025-06-09 22:57:53 - rename_pdf - DEBUG - Generating content with openai
2025-06-09 22:57:53 - rename_pdf - DEBUG - Using image data, size: 215662 bytes
2025-06-09 22:57:53 - rename_pdf - DEBUG - Calling OpenAI API...
2025-06-09 22:57:53 - rename_pdf - DEBUG - OpenAI client type: modern
2025-06-09 22:57:58 - rename_pdf - DEBUG - OpenAI response length: 85
2025-06-09 22:57:58 - rename_pdf - INFO - AI generation completed in 5.32 seconds
2025-06-09 22:57:58 - rename_pdf - DEBUG - AI response preview: 1. Ngày ký: 30/05/2025  
2. Số công văn: 330/TB-TTĐT  
3. Tiêu đề công văn: THÔNG BÁO...
2025-06-09 22:57:58 - rename_pdf - INFO - Trích xuất thông tin từ ảnh hoàn tất
2025-06-09 22:57:58 - rename_pdf - DEBUG - Extracted info: 1. Ngày ký: 30/05/2025  
2. Số công văn: 330/TB-TTĐT  
3. Tiêu đề công văn: THÔNG BÁO
2025-06-09 22:57:58 - rename_pdf - INFO - Phân tích thông tin đã trích xuất...
2025-06-09 22:57:58 - rename_pdf - DEBUG - Info lines: ['1. Ngày ký: 30/05/2025  ', '2. Số công văn: 330/TB-TTĐT  ', '3. Tiêu đề công văn: THÔNG BÁO']
2025-06-09 22:57:58 - rename_pdf - INFO - Thông tin trích xuất:
2025-06-09 22:57:58 - rename_pdf - INFO -   Ngày ký: '30/05/2025  '
2025-06-09 22:57:58 - rename_pdf - INFO -   Số công văn: '330/TB-TTĐT  '
2025-06-09 22:57:58 - rename_pdf - INFO -   Tiêu đề: 'THÔNG BÁO'
2025-06-09 22:57:58 - rename_pdf - INFO - Bắt đầu tóm tắt nội dung...
2025-06-09 22:57:58 - rename_pdf - DEBUG - Generating content with openai
2025-06-09 22:57:58 - rename_pdf - DEBUG - Prompt length: 3568
2025-06-09 22:57:58 - rename_pdf - DEBUG - Calling OpenAI API...
2025-06-09 22:57:58 - rename_pdf - DEBUG - OpenAI client type: modern
2025-06-09 22:58:03 - rename_pdf - DEBUG - OpenAI response length: 744
2025-06-09 22:58:03 - rename_pdf - INFO - AI generation completed in 5.32 seconds
2025-06-09 22:58:03 - rename_pdf - DEBUG - AI response preview: Thông báo số 330/TB-TTĐT, phát hành ngày 30/05/2025, từ Trung tâm Đào tạo thuộc Ngân hàng Chính sách xã hội thông báo về việc thay đổi cán bộ tham gia khóa đào tạo về hệ thống Intellect phiên bản nâng...
2025-06-09 22:58:03 - rename_pdf - INFO - Tóm tắt hoàn tất - Độ dài: 744 ký tự
2025-06-09 22:58:03 - rename_pdf - DEBUG - Summary preview: Thông báo số 330/TB-TTĐT, phát hành ngày 30/05/2025, từ Trung tâm Đào tạo thuộc Ngân hàng Chính sách xã hội thông báo về việc thay đổi cán bộ tham gia khóa đào tạo về hệ thống Intellect phiên bản nâng...
2025-06-09 22:58:03 - rename_pdf - INFO - Làm sạch và xử lý thông tin...
2025-06-09 22:58:03 - rename_pdf - DEBUG - Date cleaned: '30/05/2025  ' -> '30/05/2025'
2025-06-09 22:58:04 - rename_pdf - DEBUG - Date parsed: 2025-05-30 00:00:00
2025-06-09 22:58:04 - rename_pdf - DEBUG - Cleaning filename: '330/TB-TTĐT  '
2025-06-09 22:58:04 - rename_pdf - DEBUG - Cleaned filename: '330/TB-TTĐT  ' -> '330.TB-TTĐT'
2025-06-09 22:58:04 - rename_pdf - DEBUG - Cleaning filename: 'THÔNG BÁO'
2025-06-09 22:58:04 - rename_pdf - DEBUG - Cleaned filename: 'THÔNG BÁO' -> 'THÔNG BÁO'
2025-06-09 22:58:04 - rename_pdf - DEBUG - Generated filename: '20250530.330.TB-TTĐT THÔNG BÁO'
2025-06-09 22:58:04 - rename_pdf - INFO - Đổi tên file: 20250530.330.TB-TTĐT THÔNG BÁO Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf -> 20250530.330.TB-TTĐT THÔNG BÁO.pdf
2025-06-09 22:58:04 - rename_pdf - INFO - Đổi tên file thành công
2025-06-09 22:58:04 - rename_pdf - INFO - Bắt đầu ghi dữ liệu vào Google Sheet...
2025-06-09 22:58:04 - rename_pdf - DEBUG - Sheet URL: https://docs.google.com/spreadsheets/d/1t6TfA-MhuBJpArG547uandO_SgtiGGWi7Qh7UDtxp7E/edit?usp=sharing
2025-06-09 22:58:04 - rename_pdf - DEBUG - Credentials file: G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/Json/soy-tube-461903-r0-34d09c650c6b.json
2025-06-09 22:58:04 - rename_pdf - DEBUG - Row data: ['30/05/2025', '330.TB-TTĐT', 'THÔNG BÁO', 'Thông báo số 330/TB-TTĐT, phát hành ngày 30/05/2025, từ Trung tâm Đào tạo thuộc Ngân hàng Chính sách xã hội thông báo về việc thay đổi cán bộ tham gia khóa đào tạo về hệ thống Intellect phiên bản nâng cấp. Khóa đào tạo trước đó đã được thông báo vào ngày 29/05/2025 nhưng đã có đề nghị từ các chi nhánh NHCSXH tại Thái Bình, Hải Phòng và Huế về việc điều chỉnh cán bộ và thời gian tham gia.\n\nTheo đó, thời gian và địa điểm khóa đào tạo sẽ được điều chỉnh như sau:\n- Lớp thứ nhất diễn ra từ ngày 02/06 - 07/06/2025 tại Nghệ An.\n- Lớp thứ hai diễn ra từ ngày 09/06 - 14/06/2025 tại Cần Thơ.\n\nCán bộ tham gia cũng có sự thay đổi cụ thể từ các chi nhánh. Trung tâm đề nghị các giám đốc chi nhánh thông báo cho cán bộ để tham gia đầy đủ và kịp thời.', 'file:///E:/app/htdocs/Dự án đọc tài liệu/20250530.330.TB-TTĐT THÔNG BÁO.pdf']
2025-06-09 22:58:05 - rename_pdf - INFO - Ghi Google Sheet hoàn tất trong 1.10s
2025-06-09 22:58:05 - rename_pdf - INFO - ============================================================
2025-06-09 22:58:05 - rename_pdf - INFO - XỬ LÝ HOÀN TẤT THÀNH CÔNG!
2025-06-09 22:58:05 - rename_pdf - INFO - File mới: E:\app\htdocs\Dự án đọc tài liệu\20250530.330.TB-TTĐT THÔNG BÁO.pdf
2025-06-09 22:58:05 - rename_pdf - INFO - Google Sheet: aaUntitled spreadsheet
2025-06-09 22:58:05 - rename_pdf - INFO - Tổng thời gian: 16.89s
2025-06-09 22:58:05 - rename_pdf - INFO - ============================================================
2025-06-09 22:58:05 - rename_pdf - INFO - PDF processing completed
2025-06-09 22:58:15 - rename_pdf - INFO - Progress dialog completed
2025-06-09 22:58:15 - rename_pdf - INFO - KẾT THÚC RENAME_PDF.PYW
2025-06-09 22:58:15 - rename_pdf - INFO - ============================================================
2025-06-09 22:59:22 - rename_pdf - INFO - ============================================================
2025-06-09 22:59:22 - rename_pdf - INFO - KHỞI ĐỘNG RENAME_PDF.PYW (NO CONSOLE)
2025-06-09 22:59:22 - rename_pdf - INFO - Python version: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-06-09 22:59:22 - rename_pdf - INFO - Script path: E:\app\htdocs\Dự án đọc tài liệu\rename_pdf.pyw
2025-06-09 22:59:22 - rename_pdf - INFO - Working directory: E:\app\htdocs\Dự án đọc tài liệu
2025-06-09 22:59:22 - rename_pdf - INFO - Arguments: ['E:\\app\\htdocs\\Dự án đọc tài liệu\\rename_pdf.pyw', 'E:\\app\\htdocs\\Dự án đọc tài liệu\\20250530.330.TB-TTĐT THÔNG BÁO.pdf']
2025-06-09 22:59:22 - rename_pdf - INFO - ============================================================
2025-06-09 22:59:22 - rename_pdf - INFO - PDF path từ argument: E:\app\htdocs\Dự án đọc tài liệu\20250530.330.TB-TTĐT THÔNG BÁO.pdf
2025-06-09 22:59:22 - rename_pdf - INFO - File PDF hợp lệ, bắt đầu xử lý...
2025-06-09 22:59:22 - rename_pdf - INFO - Starting process_pdf wrapper for: E:\app\htdocs\Dự án đọc tài liệu\20250530.330.TB-TTĐT THÔNG BÁO.pdf
2025-06-09 22:59:22 - rename_pdf - INFO - Progress manager created successfully
2025-06-09 22:59:22 - rename_pdf - INFO - Starting PDF processing...
2025-06-09 22:59:22 - rename_pdf - INFO - ============================================================
2025-06-09 22:59:22 - rename_pdf - INFO - BẮT ĐẦU XỬ LÝ PDF: E:\app\htdocs\Dự án đọc tài liệu\20250530.330.TB-TTĐT THÔNG BÁO.pdf
2025-06-09 22:59:22 - rename_pdf - INFO - ============================================================
2025-06-09 22:59:22 - rename_pdf - INFO - File PDF: 20250530.330.TB-TTĐT THÔNG BÁO.pdf
2025-06-09 22:59:22 - rename_pdf - INFO - Kích thước: 92,217 bytes (0.09 MB)
2025-06-09 22:59:22 - rename_pdf - INFO - Script directory: E:\app\htdocs\Dự án đọc tài liệu
2025-06-09 22:59:22 - rename_pdf - INFO - Config file: E:\app\htdocs\Dự án đọc tài liệu\config.json
2025-06-09 22:59:22 - rename_pdf - INFO - Kiểm tra kết nối các dịch vụ...
2025-06-09 22:59:25 - rename_pdf - DEBUG - Kết quả kiểm tra kết nối:
2025-06-09 22:59:25 - rename_pdf - DEBUG -   google_api: ✓ - Kết nối Google API thành công
2025-06-09 22:59:25 - rename_pdf - DEBUG -   google_vision: ✓ - Kết nối Google Vision API thành công
2025-06-09 22:59:25 - rename_pdf - DEBUG -   openai: ✓ - Kết nối OpenAI API thành công (v1.84.0, model: gpt-4o-mini)
2025-06-09 22:59:25 - rename_pdf - DEBUG -   grok: ✗ - Không thể kết nối với bất kỳ Grok model nào. Lỗi cuối: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}}
2025-06-09 22:59:25 - rename_pdf - DEBUG -   sheet: ✓ - Kết nối Google Sheet thành công: aaUntitled spreadsheet
2025-06-09 22:59:25 - rename_pdf - INFO - Tất cả kết nối đều thành công!
2025-06-09 22:59:25 - rename_pdf - INFO - Bắt đầu chuyển đổi PDF thành ảnh...
2025-06-09 22:59:26 - rename_pdf - INFO - Chuyển đổi PDF hoàn tất trong 1.04s - Số trang: 2
2025-06-09 22:59:26 - rename_pdf - INFO - Bắt đầu OCR với Google Vision...
2025-06-09 22:59:26 - rename_pdf - DEBUG - Credentials file: G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/Json/soy-tube-461903-r0-34d09c650c6b.json
2025-06-09 22:59:26 - rename_pdf - DEBUG - Vision client khởi tạo thành công
2025-06-09 22:59:26 - rename_pdf - DEBUG - OCR trang 1/2
2025-06-09 22:59:27 - rename_pdf - DEBUG - Trang 1 OCR hoàn tất trong 0.72s - 2158 ký tự
2025-06-09 22:59:27 - rename_pdf - DEBUG - OCR trang 2/2
2025-06-09 22:59:27 - rename_pdf - DEBUG - Trang 2 OCR hoàn tất trong 0.48s - 1372 ký tự
2025-06-09 22:59:27 - rename_pdf - INFO - OCR hoàn tất trong 1.25s - Tổng 3,531 ký tự
2025-06-09 22:59:27 - rename_pdf - DEBUG - Text preview: NGÂN HÀNG
CHÍNH SÁCH XÃ HỘI
TRUNG TÂM ĐÀO TẠO
Số: 330/TB-TTĐT
CỘNG HOÀ XÃ HỘI CHỦ NGHĨA VIỆT NAM
Độc lập - Tự do - Hạnh phúc
Hà Nội, ngày 30 tháng 05 năm 2025
THÔNG BÁO
Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo
tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp
Kính gửi: Giám đốc Chi nhánh NHCSXH các tỉnh, thành phố: Thái Bình;
Đắk Lắk; Huế và Hải Phòng
Thực hiện Kế hoạch tổ chức khóa đào tạo vận hành hệ thống Intellect phiên
bản nâng cấp đã được T...
2025-06-09 22:59:27 - rename_pdf - INFO - Khởi tạo AI client...
2025-06-09 22:59:27 - rename_pdf - INFO - Khởi tạo AI client - Provider: openai, Model: gpt-4o
2025-06-09 22:59:27 - rename_pdf - DEBUG - OpenAI API key length: 164
2025-06-09 22:59:27 - rename_pdf - INFO - OpenAI version detected: 1.84.0
2025-06-09 22:59:28 - rename_pdf - INFO - Khởi tạo OpenAI client thành công (modern v1.0+)
2025-06-09 22:59:28 - rename_pdf - INFO - Bắt đầu trích xuất thông tin từ ảnh trang đầu...
2025-06-09 22:59:28 - rename_pdf - DEBUG - First page image size: 215662 bytes
2025-06-09 22:59:28 - rename_pdf - DEBUG - Generating content with openai
2025-06-09 22:59:28 - rename_pdf - DEBUG - Using image data, size: 215662 bytes
2025-06-09 22:59:28 - rename_pdf - DEBUG - Calling OpenAI API...
2025-06-09 22:59:28 - rename_pdf - DEBUG - OpenAI client type: modern
2025-06-09 22:59:32 - rename_pdf - DEBUG - OpenAI response length: 212
2025-06-09 22:59:32 - rename_pdf - INFO - AI generation completed in 4.10 seconds
2025-06-09 22:59:32 - rename_pdf - DEBUG - AI response preview: 1. Ngày ký: 30/05/2025
2. Số công văn: 330/TB-TTĐT
3. Tiêu đề công văn: Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên ...
2025-06-09 22:59:32 - rename_pdf - INFO - Trích xuất thông tin từ ảnh hoàn tất
2025-06-09 22:59:32 - rename_pdf - DEBUG - Extracted info: 1. Ngày ký: 30/05/2025
2. Số công văn: 330/TB-TTĐT
3. Tiêu đề công văn: Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp
2025-06-09 22:59:32 - rename_pdf - INFO - Phân tích thông tin đã trích xuất...
2025-06-09 22:59:32 - rename_pdf - DEBUG - Info lines: ['1. Ngày ký: 30/05/2025', '2. Số công văn: 330/TB-TTĐT', '3. Tiêu đề công văn: Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp']
2025-06-09 22:59:32 - rename_pdf - INFO - Thông tin trích xuất:
2025-06-09 22:59:32 - rename_pdf - INFO -   Ngày ký: '30/05/2025'
2025-06-09 22:59:32 - rename_pdf - INFO -   Số công văn: '330/TB-TTĐT'
2025-06-09 22:59:32 - rename_pdf - INFO -   Tiêu đề: 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-09 22:59:32 - rename_pdf - INFO - Bắt đầu tóm tắt nội dung...
2025-06-09 22:59:32 - rename_pdf - DEBUG - Generating content with openai
2025-06-09 22:59:32 - rename_pdf - DEBUG - Prompt length: 3568
2025-06-09 22:59:32 - rename_pdf - DEBUG - Calling OpenAI API...
2025-06-09 22:59:32 - rename_pdf - DEBUG - OpenAI client type: modern
2025-06-09 22:59:36 - rename_pdf - DEBUG - OpenAI response length: 715
2025-06-09 22:59:36 - rename_pdf - INFO - AI generation completed in 4.16 seconds
2025-06-09 22:59:36 - rename_pdf - DEBUG - AI response preview: Ngân hàng Chính sách Xã hội, Trung tâm Đào tạo thông báo về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp. T...
2025-06-09 22:59:36 - rename_pdf - INFO - Tóm tắt hoàn tất - Độ dài: 715 ký tự
2025-06-09 22:59:36 - rename_pdf - DEBUG - Summary preview: Ngân hàng Chính sách Xã hội, Trung tâm Đào tạo thông báo về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp. T...
2025-06-09 22:59:36 - rename_pdf - INFO - Làm sạch và xử lý thông tin...
2025-06-09 22:59:36 - rename_pdf - DEBUG - Date cleaned: '30/05/2025' -> '30/05/2025'
2025-06-09 22:59:36 - rename_pdf - DEBUG - Date parsed: 2025-05-30 00:00:00
2025-06-09 22:59:36 - rename_pdf - DEBUG - Cleaning filename: '330/TB-TTĐT'
2025-06-09 22:59:36 - rename_pdf - DEBUG - Cleaned filename: '330/TB-TTĐT' -> '330.TB-TTĐT'
2025-06-09 22:59:36 - rename_pdf - DEBUG - Cleaning filename: 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-09 22:59:36 - rename_pdf - DEBUG - Cleaned filename: 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp' -> 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-09 22:59:36 - rename_pdf - DEBUG - Generated filename: '20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-09 22:59:36 - rename_pdf - INFO - Đổi tên file: 20250530.330.TB-TTĐT THÔNG BÁO.pdf -> 20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-09 22:59:36 - rename_pdf - INFO - Đổi tên file thành công
2025-06-09 22:59:36 - rename_pdf - INFO - Bắt đầu ghi dữ liệu vào Google Sheet...
2025-06-09 22:59:36 - rename_pdf - DEBUG - Sheet URL: https://docs.google.com/spreadsheets/d/1t6TfA-MhuBJpArG547uandO_SgtiGGWi7Qh7UDtxp7E/edit?usp=sharing
2025-06-09 22:59:36 - rename_pdf - DEBUG - Credentials file: G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/Json/soy-tube-461903-r0-34d09c650c6b.json
2025-06-09 22:59:37 - rename_pdf - DEBUG - Row data: ['30/05/2025', '330.TB-TTĐT', 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp', 'Ngân hàng Chính sách Xã hội, Trung tâm Đào tạo thông báo về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp. Theo kế hoạch tổ chức đã được phê duyệt, có sự thay đổi về thời gian và địa điểm đào tạo cho các chi nhánh NHCSXH của thành phố Hải Phòng, tỉnh Đắk Lắk, và thành phố Huế. Cụ thể, thời gian và địa điểm đào tạo đã được điều chỉnh để phù hợp với từng chi nhánh. Ngoài ra, có sự thay đổi về cán bộ tham gia khóa học với việc thay thế học viên từ Chi nhánh NHCSXH tỉnh Thái Bình. Chi tiết sẽ được thực hiện theo thông báo trước đó. Các chi nhánh liên quan cần thông báo cho cán bộ để tham gia khóa học đầy đủ và kịp thời.', 'file:///E:/app/htdocs/Dự án đọc tài liệu/20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf']
2025-06-09 22:59:37 - rename_pdf - INFO - Ghi Google Sheet hoàn tất trong 1.07s
2025-06-09 22:59:37 - rename_pdf - INFO - ============================================================
2025-06-09 22:59:37 - rename_pdf - INFO - XỬ LÝ HOÀN TẤT THÀNH CÔNG!
2025-06-09 22:59:37 - rename_pdf - INFO - File mới: E:\app\htdocs\Dự án đọc tài liệu\20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-09 22:59:37 - rename_pdf - INFO - Google Sheet: aaUntitled spreadsheet
2025-06-09 22:59:37 - rename_pdf - INFO - Tổng thời gian: 14.98s
2025-06-09 22:59:37 - rename_pdf - INFO - ============================================================
2025-06-09 22:59:37 - rename_pdf - INFO - PDF processing completed
2025-06-09 22:59:50 - rename_pdf - INFO - Progress dialog completed
2025-06-09 22:59:50 - rename_pdf - INFO - KẾT THÚC RENAME_PDF.PYW
2025-06-09 22:59:50 - rename_pdf - INFO - ============================================================
2025-06-10 23:26:04 - rename_pdf - INFO - ============================================================
2025-06-10 23:26:04 - rename_pdf - INFO - KHỞI ĐỘNG RENAME_PDF.PY
2025-06-10 23:26:04 - rename_pdf - INFO - Python version: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-06-10 23:26:04 - rename_pdf - INFO - Script path: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\rename_pdf.py
2025-06-10 23:26:04 - rename_pdf - INFO - Working directory: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu
2025-06-10 23:26:04 - rename_pdf - INFO - Arguments: ['G:\\My Drive\\2. Cá nhân\\6. Test viết code\\Dự án đọc tài liệu\\rename_pdf.py', 'G:\\My Drive\\2. Cá nhân\\6. Test viết code\\Dự án đọc tài liệu\\20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf']
2025-06-10 23:26:04 - rename_pdf - INFO - ============================================================
2025-06-10 23:26:04 - rename_pdf - INFO - PDF path từ argument: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:26:04 - rename_pdf - INFO - File PDF hợp lệ, bắt đầu xử lý...
2025-06-10 23:26:04 - rename_pdf - INFO - Starting process_pdf wrapper for: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:26:05 - rename_pdf - INFO - Progress manager created successfully
2025-06-10 23:26:05 - rename_pdf - INFO - Starting PDF processing...
2025-06-10 23:26:05 - rename_pdf - INFO - ============================================================
2025-06-10 23:26:05 - rename_pdf - INFO - BẮT ĐẦU XỬ LÝ PDF: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:26:05 - rename_pdf - INFO - ============================================================
2025-06-10 23:26:05 - rename_pdf - INFO - File PDF: 20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:26:05 - rename_pdf - INFO - Kích thước: 92,217 bytes (0.09 MB)
2025-06-10 23:26:05 - rename_pdf - INFO - Script directory: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu
2025-06-10 23:26:05 - rename_pdf - INFO - Config file: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\config.json
2025-06-10 23:26:05 - rename_pdf - INFO - Bỏ qua bước kiểm tra kết nối các dịch vụ...
2025-06-10 23:26:05 - rename_pdf - INFO - Tiếp tục xử lý mà không kiểm tra kết nối trước
2025-06-10 23:26:05 - rename_pdf - INFO - Bắt đầu chuyển đổi PDF thành ảnh...
2025-06-10 23:26:05 - rename_pdf - INFO - Chuyển đổi PDF hoàn tất trong 0.35s - Số trang: 2
2025-06-10 23:26:05 - rename_pdf - INFO - Bắt đầu OCR với Google Vision...
2025-06-10 23:26:05 - rename_pdf - DEBUG - Credentials file: G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/Json/soy-tube-461903-r0-34d09c650c6b.json
2025-06-10 23:26:05 - rename_pdf - DEBUG - Vision client khởi tạo thành công
2025-06-10 23:26:05 - rename_pdf - DEBUG - OCR trang 1/2
2025-06-10 23:26:06 - rename_pdf - DEBUG - Trang 1 OCR hoàn tất trong 0.86s - 2158 ký tự
2025-06-10 23:26:06 - rename_pdf - DEBUG - OCR trang 2/2
2025-06-10 23:26:06 - rename_pdf - DEBUG - Trang 2 OCR hoàn tất trong 0.43s - 1372 ký tự
2025-06-10 23:26:06 - rename_pdf - INFO - OCR hoàn tất trong 1.34s - Tổng 3,531 ký tự
2025-06-10 23:26:06 - rename_pdf - DEBUG - Text preview: NGÂN HÀNG
CHÍNH SÁCH XÃ HỘI
TRUNG TÂM ĐÀO TẠO
Số: 330/TB-TTĐT
CỘNG HOÀ XÃ HỘI CHỦ NGHĨA VIỆT NAM
Độc lập - Tự do - Hạnh phúc
Hà Nội, ngày 30 tháng 05 năm 2025
THÔNG BÁO
Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo
tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp
Kính gửi: Giám đốc Chi nhánh NHCSXH các tỉnh, thành phố: Thái Bình;
Đắk Lắk; Huế và Hải Phòng
Thực hiện Kế hoạch tổ chức khóa đào tạo vận hành hệ thống Intellect phiên
bản nâng cấp đã được T...
2025-06-10 23:26:06 - rename_pdf - INFO - Khởi tạo AI client...
2025-06-10 23:26:06 - rename_pdf - INFO - Khởi tạo AI client - Provider: gemini, Model: gemini-2.5-flash-preview-05-20
2025-06-10 23:26:06 - rename_pdf - DEBUG - Gemini API key length: 39
2025-06-10 23:26:06 - rename_pdf - INFO - Khởi tạo Gemini client thành công
2025-06-10 23:26:06 - rename_pdf - INFO - Bắt đầu trích xuất thông tin từ ảnh trang đầu...
2025-06-10 23:26:06 - rename_pdf - DEBUG - First page image size: 215662 bytes
2025-06-10 23:26:06 - rename_pdf - DEBUG - Generating content with gemini
2025-06-10 23:26:06 - rename_pdf - DEBUG - Using image data, size: 215662 bytes
2025-06-10 23:26:06 - rename_pdf - DEBUG - Calling Gemini API...
2025-06-10 23:26:12 - rename_pdf - DEBUG - Gemini response length: 212
2025-06-10 23:26:12 - rename_pdf - INFO - AI generation completed in 5.64 seconds
2025-06-10 23:26:12 - rename_pdf - DEBUG - AI response preview: 1. Ngày ký: 30/05/2025
2. Số công văn: 330/TB-TTĐT
3. Tiêu đề công văn: Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên ...
2025-06-10 23:26:12 - rename_pdf - INFO - Trích xuất thông tin từ ảnh hoàn tất
2025-06-10 23:26:12 - rename_pdf - DEBUG - Extracted info: 1. Ngày ký: 30/05/2025
2. Số công văn: 330/TB-TTĐT
3. Tiêu đề công văn: Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp
2025-06-10 23:26:12 - rename_pdf - INFO - Phân tích thông tin đã trích xuất...
2025-06-10 23:26:12 - rename_pdf - DEBUG - Info lines: ['1. Ngày ký: 30/05/2025', '2. Số công văn: 330/TB-TTĐT', '3. Tiêu đề công văn: Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp']
2025-06-10 23:26:12 - rename_pdf - INFO - Thông tin trích xuất:
2025-06-10 23:26:12 - rename_pdf - INFO -   Ngày ký: '30/05/2025'
2025-06-10 23:26:12 - rename_pdf - INFO -   Số công văn: '330/TB-TTĐT'
2025-06-10 23:26:12 - rename_pdf - INFO -   Tiêu đề: 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-10 23:26:12 - rename_pdf - INFO - Bắt đầu tóm tắt nội dung...
2025-06-10 23:26:12 - rename_pdf - DEBUG - Generating content with gemini
2025-06-10 23:26:12 - rename_pdf - DEBUG - Prompt length: 3568
2025-06-10 23:26:12 - rename_pdf - DEBUG - Calling Gemini API...
2025-06-10 23:26:21 - rename_pdf - DEBUG - Gemini response length: 1030
2025-06-10 23:26:21 - rename_pdf - INFO - AI generation completed in 8.91 seconds
2025-06-10 23:26:21 - rename_pdf - DEBUG - AI response preview: Trung tâm Đào tạo, Ngân hàng Chính sách xã hội (NHCSXH) đã ban hành Thông báo số 330/TB-TTĐT ngày 30/5/2025 gửi các Giám đốc Chi nhánh NHCSXH tỉnh/thành phố Thái Bình, Đắk Lắk, Huế và Hải Phòng.

Thôn...
2025-06-10 23:26:21 - rename_pdf - INFO - Tóm tắt hoàn tất - Độ dài: 1030 ký tự
2025-06-10 23:26:21 - rename_pdf - DEBUG - Summary preview: Trung tâm Đào tạo, Ngân hàng Chính sách xã hội (NHCSXH) đã ban hành Thông báo số 330/TB-TTĐT ngày 30/5/2025 gửi các Giám đốc Chi nhánh NHCSXH tỉnh/thành phố Thái Bình, Đắk Lắk, Huế và Hải Phòng.

Thôn...
2025-06-10 23:26:21 - rename_pdf - INFO - Làm sạch và xử lý thông tin...
2025-06-10 23:26:21 - rename_pdf - DEBUG - Date cleaned: '30/05/2025' -> '30/05/2025'
2025-06-10 23:26:21 - rename_pdf - DEBUG - Date parsed: 2025-05-30 00:00:00
2025-06-10 23:26:21 - rename_pdf - DEBUG - Cleaning filename: '330/TB-TTĐT'
2025-06-10 23:26:21 - rename_pdf - DEBUG - Cleaned filename: '330/TB-TTĐT' -> '330.TB-TTĐT'
2025-06-10 23:26:21 - rename_pdf - DEBUG - Cleaning filename: 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-10 23:26:21 - rename_pdf - DEBUG - Cleaned filename: 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp' -> 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-10 23:26:21 - rename_pdf - DEBUG - Generated filename: '20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-10 23:26:21 - rename_pdf - INFO - Đổi tên file: 20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf -> 20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:26:21 - rename_pdf - INFO - Đổi tên file thành công
2025-06-10 23:26:21 - rename_pdf - INFO - Bắt đầu ghi dữ liệu vào Google Sheet...
2025-06-10 23:26:21 - rename_pdf - DEBUG - Sheet URL: https://docs.google.com/spreadsheets/d/1t6TfA-MhuBJpArG547uandO_SgtiGGWi7Qh7UDtxp7E/edit?usp=sharing
2025-06-10 23:26:21 - rename_pdf - DEBUG - Credentials file: G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/Json/soy-tube-461903-r0-34d09c650c6b.json
2025-06-10 23:26:22 - rename_pdf - DEBUG - Row data: ['30/05/2025', '330.TB-TTĐT', 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp', 'Trung tâm Đào tạo, Ngân hàng Chính sách xã hội (NHCSXH) đã ban hành Thông báo số 330/TB-TTĐT ngày 30/5/2025 gửi các Giám đốc Chi nhánh NHCSXH tỉnh/thành phố Thái Bình, Đắk Lắk, Huế và Hải Phòng.\n\nThông báo này về việc điều chỉnh cán bộ, thời gian và địa điểm tham gia khóa đào tạo "tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp". Việc điều chỉnh được thực hiện do các chi nhánh có đề nghị thay đổi nhân sự hoặc thời gian, địa điểm học.\n\nCụ thể, thông báo điều chỉnh:\n1.  **Thời gian và địa điểm học:** Một số cán bộ thuộc các chi nhánh Hải Phòng, Đắk Lắk, Huế được thay đổi từ Lớp thứ nhất sang Lớp thứ hai (hoặc ngược lại) với địa điểm học tương ứng (Nghệ An hoặc Cần Thơ).\n2.  **Cán bộ tham gia:** Chi nhánh NHCSXH tỉnh Thái Bình thay đổi cán bộ tham gia khóa đào tạo, cụ thể là ông Phạm Trung Kiên (Trưởng phòng Tin học) được thay thế bằng bà Bùi Thị Lan Hương (Cán bộ phòng Tin học).\n\nTrung tâm Đào tạo đề nghị các chi nhánh thông báo cho cán bộ liên quan biết để tham dự khóa học đầy đủ và kịp thời.', 'file:///G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf']
2025-06-10 23:26:22 - rename_pdf - INFO - Ghi Google Sheet hoàn tất trong 1.27s
2025-06-10 23:26:22 - rename_pdf - INFO - ============================================================
2025-06-10 23:26:22 - rename_pdf - INFO - XỬ LÝ HOÀN TẤT THÀNH CÔNG!
2025-06-10 23:26:22 - rename_pdf - INFO - File mới: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:26:22 - rename_pdf - INFO - Google Sheet: Tổng hợp công văn
2025-06-10 23:26:22 - rename_pdf - INFO - Tổng thời gian: 17.68s
2025-06-10 23:26:22 - rename_pdf - INFO - ============================================================
2025-06-10 23:26:22 - rename_pdf - INFO - PDF processing completed
2025-06-10 23:26:26 - rename_pdf - INFO - Progress dialog completed
2025-06-10 23:26:26 - rename_pdf - INFO - KẾT THÚC RENAME_PDF.PY
2025-06-10 23:26:26 - rename_pdf - INFO - ============================================================
2025-06-10 23:40:40 - rename_pdf - INFO - ============================================================
2025-06-10 23:40:40 - rename_pdf - INFO - KHỞI ĐỘNG RENAME_PDF.PY
2025-06-10 23:40:40 - rename_pdf - INFO - Python version: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-06-10 23:40:40 - rename_pdf - INFO - Script path: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\rename_pdf.py
2025-06-10 23:40:40 - rename_pdf - INFO - Working directory: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu
2025-06-10 23:40:40 - rename_pdf - INFO - Arguments: ['G:\\My Drive\\2. Cá nhân\\6. Test viết code\\Dự án đọc tài liệu\\rename_pdf.py']
2025-06-10 23:40:40 - rename_pdf - INFO - ============================================================
2025-06-10 23:40:40 - rename_pdf - ERROR - Vui lòng cung cấp đường dẫn file PDF
2025-06-10 23:40:40 - rename_pdf - INFO - Cách sử dụng: python rename_pdf.py <path_to_pdf_file>
2025-06-10 23:40:40 - rename_pdf - INFO - Notification: Lỗi - Vui lòng cung cấp đường dẫn file PDF
2025-06-10 23:40:41 - rename_pdf - INFO - KẾT THÚC RENAME_PDF.PY
2025-06-10 23:40:41 - rename_pdf - INFO - ============================================================
2025-06-10 23:52:47 - rename_pdf - INFO - ============================================================
2025-06-10 23:52:47 - rename_pdf - INFO - KHỞI ĐỘNG RENAME_PDF.PY
2025-06-10 23:52:47 - rename_pdf - INFO - Python version: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-06-10 23:52:47 - rename_pdf - INFO - Script path: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\rename_pdf.py
2025-06-10 23:52:47 - rename_pdf - INFO - Working directory: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu
2025-06-10 23:52:47 - rename_pdf - INFO - Arguments: ['G:\\My Drive\\2. Cá nhân\\6. Test viết code\\Dự án đọc tài liệu\\rename_pdf.py', 'G:\\My Drive\\2. Cá nhân\\6. Test viết code\\Dự án đọc tài liệu\\20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf']
2025-06-10 23:52:47 - rename_pdf - INFO - ============================================================
2025-06-10 23:52:47 - rename_pdf - INFO - PDF path từ argument: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:52:47 - rename_pdf - INFO - File PDF hợp lệ, bắt đầu xử lý...
2025-06-10 23:52:47 - rename_pdf - INFO - Starting process_pdf wrapper for: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:52:47 - rename_pdf - INFO - Progress manager created successfully
2025-06-10 23:52:47 - rename_pdf - INFO - Starting PDF processing...
2025-06-10 23:52:47 - rename_pdf - INFO - ============================================================
2025-06-10 23:52:47 - rename_pdf - INFO - BẮT ĐẦU XỬ LÝ PDF: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:52:47 - rename_pdf - INFO - ============================================================
2025-06-10 23:52:47 - rename_pdf - INFO - File PDF: 20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:52:47 - rename_pdf - INFO - Kích thước: 92,217 bytes (0.09 MB)
2025-06-10 23:52:47 - rename_pdf - INFO - Script directory: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu
2025-06-10 23:52:47 - rename_pdf - INFO - Config file: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\config.json
2025-06-10 23:52:47 - rename_pdf - INFO - Bỏ qua bước kiểm tra kết nối các dịch vụ...
2025-06-10 23:52:47 - rename_pdf - INFO - Tiếp tục xử lý mà không kiểm tra kết nối trước
2025-06-10 23:52:47 - rename_pdf - INFO - Bắt đầu chuyển đổi PDF thành ảnh...
2025-06-10 23:52:47 - rename_pdf - INFO - Chuyển đổi PDF hoàn tất trong 0.38s - Số trang: 2
2025-06-10 23:52:47 - rename_pdf - INFO - Bắt đầu OCR với Google Vision...
2025-06-10 23:52:47 - rename_pdf - DEBUG - Credentials file: G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/Json/soy-tube-461903-r0-34d09c650c6b.json
2025-06-10 23:52:48 - rename_pdf - DEBUG - Vision client khởi tạo thành công
2025-06-10 23:52:48 - rename_pdf - DEBUG - OCR trang 1/2
2025-06-10 23:52:48 - rename_pdf - DEBUG - Trang 1 OCR hoàn tất trong 0.78s - 2158 ký tự
2025-06-10 23:52:48 - rename_pdf - DEBUG - OCR trang 2/2
2025-06-10 23:52:49 - rename_pdf - DEBUG - Trang 2 OCR hoàn tất trong 0.43s - 1372 ký tự
2025-06-10 23:52:49 - rename_pdf - INFO - OCR hoàn tất trong 1.28s - Tổng 3,531 ký tự
2025-06-10 23:52:49 - rename_pdf - DEBUG - Text preview: NGÂN HÀNG
CHÍNH SÁCH XÃ HỘI
TRUNG TÂM ĐÀO TẠO
Số: 330/TB-TTĐT
CỘNG HOÀ XÃ HỘI CHỦ NGHĨA VIỆT NAM
Độc lập - Tự do - Hạnh phúc
Hà Nội, ngày 30 tháng 05 năm 2025
THÔNG BÁO
Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo
tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp
Kính gửi: Giám đốc Chi nhánh NHCSXH các tỉnh, thành phố: Thái Bình;
Đắk Lắk; Huế và Hải Phòng
Thực hiện Kế hoạch tổ chức khóa đào tạo vận hành hệ thống Intellect phiên
bản nâng cấp đã được T...
2025-06-10 23:52:49 - rename_pdf - INFO - Khởi tạo AI client...
2025-06-10 23:52:49 - rename_pdf - INFO - Khởi tạo AI client - Provider: gemini, Model: gemini-2.5-flash-preview-05-20
2025-06-10 23:52:49 - rename_pdf - DEBUG - Gemini API key length: 39
2025-06-10 23:52:49 - rename_pdf - INFO - Khởi tạo Gemini client thành công
2025-06-10 23:52:49 - rename_pdf - INFO - Bắt đầu trích xuất thông tin từ ảnh trang đầu...
2025-06-10 23:52:49 - rename_pdf - DEBUG - First page image size: 215662 bytes
2025-06-10 23:52:49 - rename_pdf - DEBUG - Generating content with gemini
2025-06-10 23:52:49 - rename_pdf - DEBUG - Using image data, size: 215662 bytes
2025-06-10 23:52:49 - rename_pdf - DEBUG - Calling Gemini API...
2025-06-10 23:52:53 - rename_pdf - DEBUG - Gemini response length: 212
2025-06-10 23:52:53 - rename_pdf - INFO - AI generation completed in 4.62 seconds
2025-06-10 23:52:53 - rename_pdf - DEBUG - AI response preview: 1. Ngày ký: 30/05/2025
2. Số công văn: 330/TB-TTĐT
3. Tiêu đề công văn: Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên ...
2025-06-10 23:52:53 - rename_pdf - INFO - Trích xuất thông tin từ ảnh hoàn tất
2025-06-10 23:52:53 - rename_pdf - DEBUG - Extracted info: 1. Ngày ký: 30/05/2025
2. Số công văn: 330/TB-TTĐT
3. Tiêu đề công văn: Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp
2025-06-10 23:52:53 - rename_pdf - INFO - Phân tích thông tin đã trích xuất...
2025-06-10 23:52:53 - rename_pdf - DEBUG - Info lines: ['1. Ngày ký: 30/05/2025', '2. Số công văn: 330/TB-TTĐT', '3. Tiêu đề công văn: Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp']
2025-06-10 23:52:53 - rename_pdf - INFO - Thông tin trích xuất:
2025-06-10 23:52:53 - rename_pdf - INFO -   Ngày ký: '30/05/2025'
2025-06-10 23:52:53 - rename_pdf - INFO -   Số công văn: '330/TB-TTĐT'
2025-06-10 23:52:53 - rename_pdf - INFO -   Tiêu đề: 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-10 23:52:53 - rename_pdf - INFO - Bắt đầu tóm tắt nội dung...
2025-06-10 23:52:53 - rename_pdf - DEBUG - Generating content with gemini
2025-06-10 23:52:53 - rename_pdf - DEBUG - Prompt length: 3568
2025-06-10 23:52:53 - rename_pdf - DEBUG - Calling Gemini API...
2025-06-10 23:53:05 - rename_pdf - DEBUG - Gemini response length: 1302
2025-06-10 23:53:05 - rename_pdf - INFO - AI generation completed in 11.60 seconds
2025-06-10 23:53:05 - rename_pdf - DEBUG - AI response preview: Thông báo số 330/TB-TTĐT của Trung tâm Đào tạo, Ngân hàng Chính sách xã hội (NHCSXH), ngày 30/05/2025, về việc điều chỉnh cán bộ, thời gian và địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề v...
2025-06-10 23:53:05 - rename_pdf - INFO - Tóm tắt hoàn tất - Độ dài: 1302 ký tự
2025-06-10 23:53:05 - rename_pdf - DEBUG - Summary preview: Thông báo số 330/TB-TTĐT của Trung tâm Đào tạo, Ngân hàng Chính sách xã hội (NHCSXH), ngày 30/05/2025, về việc điều chỉnh cán bộ, thời gian và địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề v...
2025-06-10 23:53:05 - rename_pdf - INFO - Làm sạch và xử lý thông tin...
2025-06-10 23:53:05 - rename_pdf - DEBUG - Date cleaned: '30/05/2025' -> '30/05/2025'
2025-06-10 23:53:05 - rename_pdf - DEBUG - Date parsed: 2025-05-30 00:00:00
2025-06-10 23:53:05 - rename_pdf - DEBUG - Cleaning filename: '330/TB-TTĐT'
2025-06-10 23:53:05 - rename_pdf - DEBUG - Cleaned filename: '330/TB-TTĐT' -> '330.TB-TTĐT'
2025-06-10 23:53:05 - rename_pdf - DEBUG - Cleaning filename: 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-10 23:53:05 - rename_pdf - DEBUG - Cleaned filename: 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp' -> 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-10 23:53:05 - rename_pdf - DEBUG - Generated filename: '20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp'
2025-06-10 23:53:05 - rename_pdf - INFO - Đổi tên file: 20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf -> 20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:53:05 - rename_pdf - INFO - Đổi tên file thành công
2025-06-10 23:53:05 - rename_pdf - INFO - Bắt đầu ghi dữ liệu vào Google Sheet...
2025-06-10 23:53:05 - rename_pdf - DEBUG - Sheet URL: https://docs.google.com/spreadsheets/d/1t6TfA-MhuBJpArG547uandO_SgtiGGWi7Qh7UDtxp7E/edit?usp=sharing
2025-06-10 23:53:05 - rename_pdf - DEBUG - Credentials file: G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/Json/soy-tube-461903-r0-34d09c650c6b.json
2025-06-10 23:53:06 - rename_pdf - DEBUG - Row data: ['30/05/2025', '330.TB-TTĐT', 'Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp', 'Thông báo số 330/TB-TTĐT của Trung tâm Đào tạo, Ngân hàng Chính sách xã hội (NHCSXH), ngày 30/05/2025, về việc điều chỉnh cán bộ, thời gian và địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.\n\nThông báo này được gửi đến các chi nhánh NHCSXH tỉnh/thành phố Thái Bình, Đắk Lắk, Huế và Hải Phòng. Các điều chỉnh được thực hiện dựa trên đề nghị từ các chi nhánh (Thái Bình xin thay đổi cán bộ; Hải Phòng, Huế xin điều chỉnh thời gian/địa điểm) và sau khi thống nhất với chi nhánh Đắk Lắk, đồng thời được sự chấp thuận của Phó Tổng Giám đốc.\n\nCụ thể, có hai loại điều chỉnh:\n\n1.  **Thay đổi thời gian và địa điểm:**\n    *   Cán bộ từ chi nhánh Hải Phòng và Huế (ban đầu dự kiến học Lớp thứ nhất tại Nghệ An từ 02-07/6/2025) sẽ chuyển sang học Lớp thứ hai tại Cần Thơ từ 09-14/6/2025.\n    *   Cán bộ từ chi nhánh Đắk Lắk (ban đầu dự kiến học Lớp thứ hai tại Cần Thơ từ 09-14/6/2025) sẽ chuyển sang học Lớp thứ nhất tại Nghệ An từ 02-07/6/2025.\n\n2.  **Thay đổi cán bộ tham gia:**\n    *   Chi nhánh Thái Bình thay thế cán bộ đã triệu tập là Phạm Trung Kiên (Trưởng phòng Tin học) bằng Bùi Thị Lan Hương (Cán bộ phòng Tin học).\n\nTrung tâm Đào tạo đề nghị các chi nhánh liên quan thông báo cho cán bộ của mình để đảm bảo tham dự khóa học kịp thời và đầy đủ.', 'file:///G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf']
2025-06-10 23:53:07 - rename_pdf - INFO - Ghi Google Sheet hoàn tất trong 1.76s
2025-06-10 23:53:07 - rename_pdf - INFO - ============================================================
2025-06-10 23:53:07 - rename_pdf - INFO - XỬ LÝ HOÀN TẤT THÀNH CÔNG!
2025-06-10 23:53:07 - rename_pdf - INFO - File mới: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\20250530.330.TB-TTĐT Về việc thay đổi cán bộ và thời gian, địa điểm tham gia khóa đào tạo tiểu giáo viên chuyên đề vận hành hệ thống Intellect phiên bản nâng cấp.pdf
2025-06-10 23:53:07 - rename_pdf - INFO - Google Sheet: Tổng hợp công văn
2025-06-10 23:53:07 - rename_pdf - INFO - Tổng thời gian: 19.87s
2025-06-10 23:53:07 - rename_pdf - INFO - ============================================================
2025-06-10 23:53:07 - rename_pdf - INFO - PDF processing completed
2025-06-10 23:58:19 - rename_pdf - INFO - Progress dialog completed
2025-06-10 23:58:19 - rename_pdf - INFO - KẾT THÚC RENAME_PDF.PY
2025-06-10 23:58:19 - rename_pdf - INFO - ============================================================
2025-06-10 23:58:59 - rename_pdf - INFO - ============================================================
2025-06-10 23:58:59 - rename_pdf - INFO - KHỞI ĐỘNG RENAME_PDF.PY
2025-06-10 23:58:59 - rename_pdf - INFO - Python version: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-06-10 23:58:59 - rename_pdf - INFO - Script path: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\rename_pdf.py
2025-06-10 23:58:59 - rename_pdf - INFO - Working directory: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu
2025-06-10 23:58:59 - rename_pdf - INFO - Arguments: ['G:\\My Drive\\2. Cá nhân\\6. Test viết code\\Dự án đọc tài liệu\\rename_pdf.py', 'G:\\My Drive\\2. Cá nhân\\6. Test viết code\\Dự án đọc tài liệu\\register_pdf_context_menu_new.py']
2025-06-10 23:58:59 - rename_pdf - INFO - ============================================================
2025-06-10 23:58:59 - rename_pdf - INFO - PDF path từ argument: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\register_pdf_context_menu_new.py
2025-06-10 23:58:59 - rename_pdf - ERROR - File không tồn tại hoặc không phải file PDF: G:\My Drive\2. Cá nhân\6. Test viết code\Dự án đọc tài liệu\register_pdf_context_menu_new.py
2025-06-10 23:58:59 - rename_pdf - INFO - Notification: Lỗi - File không tồn tại hoặc không phải file PDF
2025-06-10 23:59:01 - rename_pdf - INFO - KẾT THÚC RENAME_PDF.PY
2025-06-10 23:59:01 - rename_pdf - INFO - ============================================================
