<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hướng dẫn Cài đặt Ứng dụng Hỗ trợ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: auto;
        }
        h1, h2 {
            color: #007bff;
        }
        code {
            background-color: #e9e9e9;
            padding: 2px 5px;
            border-radius: 4px;
            font-family: monospace;
        }
        .note {
            background-color: #fff3cd;
            border-left: 5px solid #ffeeba;
            padding: 10px;
            margin: 15px 0;
        }
        .button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 10px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        ol li {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hướng dẫn Cài đặt Ứng dụng Hỗ trợ (Native Host)</h1>
        <p>Để tiện ích "Google Sheets File Links Handler" có thể mở các tệp cục bộ từ Google Sheets, bạn cần cài đặt một ứng dụng nhỏ trên máy tính của mình. Ứng dụng này hoạt động như một cầu nối an toàn giữa trình duyệt và hệ thống tệp của bạn.</p>

        <div class="note">
            <p><strong>Quan trọng:</strong> Ứng dụng này chỉ được thiết kế để mở các đường dẫn tệp mà bạn nhấp vào. Nó không có quyền truy cập vào các tệp khác hoặc thực hiện các hành động khác trên máy tính của bạn mà không có sự cho phép rõ ràng.</p>
        </div>

        <h2>Các bước cài đặt (Dành cho Windows)</h2>
        <ol>
            <li>
                <strong>Tải xuống tệp cài đặt:</strong>
                <p>Chúng tôi sẽ sớm cung cấp liên kết tải xuống tệp cài đặt (ví dụ: <code>setup_gsheets_file_opener.bat</code> hoặc <code>setup_gsheets_file_opener.exe</code>).</p>
                <!-- <a href="LINK_TO_DOWNLOAD_SCRIPT" class="button" download>Tải xuống Script Cài đặt (Chưa có sẵn)</a> -->
            </li>
            <li>
                <strong>Chạy tệp cài đặt:</strong>
                <p>Sau khi tải xuống, hãy chạy tệp cài đặt. Nếu đó là tệp <code>.bat</code>, bạn có thể cần nhấp chuột phải và chọn "Run as administrator" (Chạy với quyền quản trị).</p>
            </li>
            <li>
                <strong>Hoàn tất cài đặt:</strong>
                <p>Script cài đặt sẽ tự động sao chép các tệp cần thiết và đăng ký ứng dụng hỗ trợ với Google Chrome.</p>
            </li>
            <li>
                <strong>Khởi động lại Google Chrome:</strong>
                <p>Sau khi cài đặt hoàn tất, hãy đóng tất cả các cửa sổ Google Chrome và mở lại trình duyệt.</p>
            </li>
            <li>
                <strong>Kiểm tra lại tiện ích:</strong>
                <p>Thử nhấp lại vào một liên kết <code>file:///</code> trong Google Sheets. Nếu mọi thứ được cài đặt đúng cách, tệp sẽ được mở.</p>
            </li>
        </ol>

        <h2>Xử lý sự cố</h2>
        <ul>
            <li><strong>Tiện ích vẫn báo lỗi "Specified native messaging host not found":</strong> Đảm bảo bạn đã khởi động lại Chrome sau khi chạy script cài đặt. Nếu vẫn không được, hãy thử chạy lại script cài đặt với quyền quản trị.</li>
            <li><strong>Tệp không mở được:</strong> Kiểm tra xem đường dẫn tệp trong Google Sheets có chính xác không và tệp đó có tồn tại trên máy tính của bạn không.</li>
        </ul>

        <p>Nếu bạn gặp bất kỳ vấn đề nào khác, vui lòng liên hệ với chúng tôi để được hỗ trợ.</p>
    </div>
</body>
</html>
