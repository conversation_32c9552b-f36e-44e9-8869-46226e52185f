import sys
import json
import struct
import os
import subprocess

# Hàm để lấy message từ Chrome
def get_message():
    raw_length = sys.stdin.buffer.read(4)
    if not raw_length:
        sys.exit(0)
    message_length = struct.unpack('@I', raw_length)[0]
    message = sys.stdin.buffer.read(message_length).decode('utf-8')
    return json.loads(message)

# Hàm để gửi message cho Chrome
def send_message(message_content):
    encoded_content = json.dumps(message_content).encode('utf-8')
    encoded_length = struct.pack('@I', len(encoded_content))
    sys.stdout.buffer.write(encoded_length)
    sys.stdout.buffer.write(encoded_content)
    sys.stdout.buffer.flush()

def main():
    try:
        message = get_message()
        file_path = message.get('path')

        if not file_path:
            send_message({'success': False, 'error': 'No file path provided.'})
            return

        # Đả<PERSON> bảo đường dẫn là tuyệt đối và chuẩn hóa nó
        abs_path = os.path.abspath(os.path.normpath(file_path))

        if not os.path.exists(abs_path):
            send_message({'success': False, 'error': f'File not found: {abs_path}'})
            return

        # Mở file bằng ứng dụng mặc định của hệ thống
        try:
            if sys.platform == "win32":
                os.startfile(abs_path)
            elif sys.platform == "darwin": # macOS
                subprocess.call(('open', abs_path))
            else: # linux variants
                subprocess.call(('xdg-open', abs_path))
            send_message({'success': True, 'message': f'Successfully requested to open file: {abs_path}'})
        except Exception as e:
            send_message({'success': False, 'error': f'Error opening file: {str(e)}'})

    except Exception as e:
        # Gửi lỗi chung nếu có vấn đề không mong muốn
        send_message({'success': False, 'error': f'An unexpected error occurred in native host: {str(e)}'})

if __name__ == '__main__':
    main()
