// Background script để xử lý việc mở file
console.log('Google Sheets File Links Handler - Background script loaded');

class BackgroundHandler {
    constructor() {
        this.init();
    }

    init() {
        // Lắng nghe messages từ content script
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'openFile') {
                this.handleOpenFile(request.filePath, sendResponse);
                return true; // Giữ message channel mở cho async response
            }
        });

        // Thiết lập cài đặt mặc định khi cài đặt extension
        chrome.runtime.onInstalled.addListener(() => {
            chrome.storage.sync.set({
                enabled: true,
                showNotifications: true
            });
        });
    }

    async handleOpenFile(filePath, sendResponse) {
        const nativeHostName = 'com.my_company.gsheets_file_opener';
        console.log(`Attempting to send message to native host: ${nativeHostName} for path: ${filePath}`);

        // Chuyển đổi file:/// URL thành đường dẫn hệ thống
        // Ví dụ: file:///C:/Users/<USER>/Desktop/file.txt -> C:\Users\<USER>\Desktop\file.txt (Windows)
        // Ví dụ: file:///Users/<USER>/Desktop/file.txt -> /Users/<USER>/Desktop/file.txt (macOS/Linux)
        let systemPath = filePath;
        if (systemPath.startsWith('file:///')) {
            systemPath = systemPath.substring(8); // Bỏ 'file:///'
            if (systemPath.length > 2 && systemPath[1] === ':' && (systemPath[0] >= 'A' && systemPath[0] <= 'Z' || systemPath[0] >= 'a' && systemPath[0] <= 'z')) {
                // Windows path like C:/path/to/file
                systemPath = systemPath.replace(/\//g, '\\');
            } else {
                // Unix-like path /path/to/file
                // No change needed for slashes, but ensure it starts with /
                if (!systemPath.startsWith('/')) {
                    systemPath = '/' + systemPath;
                }
            }
        }
        systemPath = decodeURIComponent(systemPath);


        chrome.runtime.sendNativeMessage(
            nativeHostName,
            { path: systemPath },
            (response) => {
                if (chrome.runtime.lastError) {
                    console.error('Native Messaging Error:', chrome.runtime.lastError.message);
                    sendResponse({ success: false, error: `Native Messaging Error: ${chrome.runtime.lastError.message}` });
                    // Có thể hiển thị thông báo lỗi cho người dùng ở đây nếu cần
                    // Ví dụ: thông báo rằng ứng dụng gốc chưa được cài đặt.
                    if (chrome.runtime.lastError.message.includes("Specified native messaging host not found")) {
                        // Tạo một trang hướng dẫn cài đặt native host
                        const guideUrl = chrome.runtime.getURL("native_host_setup_guide.html");
                        chrome.tabs.create({ url: guideUrl });
                    }
                    return;
                }
                if (response) {
                    console.log('Received response from native host:', response);
                    sendResponse(response);
                } else {
                    console.error('No response from native host.');
                    sendResponse({ success: false, error: 'No response from native host.' });
                }
            }
        );
    }

    // convertFileUrlToWindowsPath không còn cần thiết vì native host sẽ xử lý đường dẫn
}

// Khởi tạo background handler
new BackgroundHandler();
