// Background script cho Google Drive PDF Downloader

// Lắng nghe tin nhắn từ content script và popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'downloadPDF') {
    downloadPDF(request.fileId, request.fileName)
      .then(() => sendResponse({success: true}))
      .catch(error => sendResponse({success: false, error: error.message}));
    return true;
  } else if (request.action === 'checkPDFAvailability') {
    checkPDFAvailability(request.fileId)
      .then(result => sendResponse(result))
      .catch(error => sendResponse({available: false, error: error.message}));
    return true;
  } else if (request.action === 'tryAdvancedBypass') {
    tryAdvancedBypassMethods(request.fileId, request.fileName)
      .then(result => sendResponse(result))
      .catch(error => sendResponse({success: false, error: error.message}));
    return true;
  } else if (request.action === 'screenshotToPDF') {
    tryScreenshotToPDF(request.fileId, request.fileName)
      .then(() => sendResponse({success: true}))
      .catch(error => sendResponse({success: false, error: error.message}));
    return true;
  }
});

// Thử các phương pháp bypass nâng cao
async function tryAdvancedBypassMethods(fileId, fileName) {
  console.log('Bắt đầu thử các phương pháp bypass nâng cao...');

  const methods = [
    () => tryGoogleCacheBypass(fileId, fileName),
    () => tryWebArchiveBypass(fileId, fileName),
    () => tryProxyBypass(fileId, fileName),
    () => tryMobileUserAgentBypass(fileId, fileName),
    () => tryEmbedBypass(fileId, fileName)
  ];

  for (let i = 0; i < methods.length; i++) {
    try {
      console.log(`Thử phương pháp bypass ${i + 1}...`);
      const result = await methods[i]();
      if (result) {
        console.log(`Phương pháp bypass ${i + 1} thành công!`);
        return {success: true, method: i + 1};
      }
    } catch (error) {
      console.log(`Phương pháp bypass ${i + 1} thất bại:`, error);
    }
  }

  return {success: false, error: 'Tất cả phương pháp bypass đều thất bại'};
}

// Hàm tải xuống PDF với các phương pháp lách
async function downloadPDF(fileId, fileName) {
  try {
    console.log('Bắt đầu tải xuống file:', fileId);

    // Tạo tên file nếu không có
    const finalFileName = fileName || `google-drive-${fileId}.pdf`;

    // Phương pháp 1: Thử các URL lách khác nhau
    const success = await tryBypassMethods(fileId, finalFileName);
    if (success) {
      console.log('Lách thành công!');
      return;
    }

    // Phương pháp 2: Capture screenshot và chuyển thành PDF
    console.log('Thử phương pháp screenshot to PDF...');
    await tryScreenshotToPDF(fileId, finalFileName);

  } catch (error) {
    console.error('Lỗi trong downloadPDF:', error);
    // Phương pháp cuối cùng: Print to PDF từ viewer
    tryViewerToPDF(fileId, fileName);
  }
}

// Thử các phương pháp lách khác nhau
async function tryBypassMethods(fileId, fileName) {
  console.log('Thử các phương pháp lách...');

  // Phương pháp 1: Thử các URL với tham số khác nhau
  const bypassUrls = [
    // URL gốc
    `https://drive.google.com/uc?export=download&id=${fileId}`,

    // Thêm tham số confirm để bypass warning
    `https://drive.google.com/uc?export=download&id=${fileId}&confirm=t`,

    // Sử dụng API endpoint khác
    `https://www.googleapis.com/drive/v3/files/${fileId}?alt=media`,

    // Thử với user agent khác (mobile)
    `https://drive.google.com/uc?export=download&id=${fileId}&usp=drive_mobile`,

    // Thử endpoint cũ
    `https://docs.google.com/uc?export=download&id=${fileId}`,

    // Thử với tham số authuser
    `https://drive.google.com/uc?export=download&id=${fileId}&authuser=0`,

    // Thử format khác nhau
    `https://drive.google.com/file/d/${fileId}/export?format=pdf&gid=0`,

    // Thử với resourcekey (cho file shared)
    `https://drive.google.com/uc?export=download&id=${fileId}&resourcekey=`,
  ];

  for (let i = 0; i < bypassUrls.length; i++) {
    try {
      console.log(`Thử phương pháp lách ${i + 1}:`, bypassUrls[i]);

      // Thử với headers khác nhau
      const success = await attemptDownloadWithHeaders(bypassUrls[i], fileName, i);
      if (success) {
        return true;
      }
    } catch (error) {
      console.log(`Phương pháp lách ${i + 1} thất bại:`, error);
    }
  }

  // Phương pháp 2: Thử lách qua embed viewer
  return await tryEmbedViewerBypass(fileId, fileName);
}

// Thử tải xuống với headers đặc biệt
async function attemptDownloadWithHeaders(url, fileName, methodIndex) {
  try {
    // Thử tải xuống trực tiếp trước
    await attemptDownload(url, fileName, methodIndex);
    return true;
  } catch (error) {
    // Nếu thất bại, thử với fetch để kiểm tra response
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://drive.google.com/',
          'Accept': 'application/pdf,*/*'
        }
      });

      if (response.ok) {
        const contentType = response.headers.get('content-type');
        const contentLength = response.headers.get('content-length');

        console.log('Response info:', {
          status: response.status,
          contentType,
          contentLength
        });

        // Kiểm tra xem có phải PDF thật không
        if (contentType && contentType.includes('application/pdf')) {
          // Tải xuống bằng Chrome Downloads API
          await attemptDownload(url, fileName, methodIndex);
          return true;
        } else {
          // Kiểm tra nội dung để xem có phải thông báo lỗi không
          const text = await response.text();
          if (text.includes('permission') || text.includes('owner')) {
            console.log('Phát hiện thông báo hạn chế quyền');
            return false;
          }
        }
      }
    } catch (fetchError) {
      console.log('Fetch error:', fetchError);
    }

    return false;
  }
}

// Thử lách qua embed viewer
async function tryEmbedViewerBypass(fileId, fileName) {
  console.log('Thử lách qua embed viewer...');

  try {
    // Tạo tab với embed viewer
    const embedUrl = `https://drive.google.com/file/d/${fileId}/preview`;

    const tab = await chrome.tabs.create({
      url: embedUrl,
      active: false
    });

    // Đợi tab load
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Inject script để lấy PDF data
    try {
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: extractPDFFromViewer
      });

      if (results && results[0] && results[0].result) {
        console.log('Lấy được PDF data từ viewer');

        // Tạo blob và tải xuống
        const pdfData = results[0].result;
        const blob = new Blob([new Uint8Array(pdfData)], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);

        await chrome.downloads.download({
          url: url,
          filename: fileName,
          saveAs: true
        });

        chrome.tabs.remove(tab.id);
        return true;
      }
    } catch (scriptError) {
      console.log('Script injection thất bại:', scriptError);
    }

    chrome.tabs.remove(tab.id);
    return false;

  } catch (error) {
    console.error('Lỗi embed viewer bypass:', error);
    return false;
  }
}

// Function để inject vào viewer page
function extractPDFFromViewer() {
  try {
    // Tìm PDF viewer element
    const viewer = document.querySelector('embed[type="application/pdf"]') ||
                  document.querySelector('iframe[src*="pdf"]') ||
                  document.querySelector('object[type="application/pdf"]');

    if (viewer && viewer.src) {
      console.log('Tìm thấy PDF viewer:', viewer.src);

      // Thử lấy PDF data
      return fetch(viewer.src)
        .then(response => response.arrayBuffer())
        .then(buffer => Array.from(new Uint8Array(buffer)))
        .catch(error => {
          console.log('Không thể lấy PDF data:', error);
          return null;
        });
    }

    return null;
  } catch (error) {
    console.log('Lỗi extract PDF:', error);
    return null;
  }
}

// Thử phương pháp HTML khác
async function tryAlternativeHTMLDownload(fileId, fileName) {
  const alternativeUrls = [
    `https://docs.google.com/document/d/${fileId}/export?format=html`,
    `https://drive.google.com/file/d/${fileId}/export?format=html`,
    `https://docs.google.com/document/d/${fileId}/pub?output=html`,
    `https://drive.google.com/uc?id=${fileId}&export=download&format=html`
  ];

  for (const url of alternativeUrls) {
    try {
      console.log('Thử URL HTML:', url);
      const response = await fetch(url);
      if (response.ok) {
        const htmlContent = await response.text();
        if (htmlContent.length > 1000) { // Đảm bảo có nội dung
          await convertHTMLToPDF(htmlContent, fileName, fileId);
          return;
        }
      }
    } catch (error) {
      console.log('URL thất bại:', url, error);
    }
  }

  // Nếu tất cả thất bại, thử phương pháp cuối cùng
  console.log('Tất cả phương pháp HTML thất bại, chuyển sang Print to PDF');
  tryPrintToPDF(fileId, fileName);
}

// Chuyển đổi HTML thành PDF
async function convertHTMLToPDF(htmlContent, fileName, fileId) {
  try {
    console.log('Bắt đầu chuyển đổi HTML thành PDF...');

    // Tạo tab ẩn để render HTML
    const tab = await chrome.tabs.create({
      url: 'data:text/html;charset=utf-8,' + encodeURIComponent(cleanHTMLForPDF(htmlContent)),
      active: false
    });

    // Đợi tab load xong
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Sử dụng Chrome's Print to PDF API
    try {
      const pdfData = await chrome.tabs.printToPDF(tab.id, {
        format: 'A4',
        printBackground: true,
        marginTop: 0.4,
        marginBottom: 0.4,
        marginLeft: 0.4,
        marginRight: 0.4
      });

      // Tạo blob URL và tải xuống
      const blob = new Blob([pdfData], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      await chrome.downloads.download({
        url: url,
        filename: fileName,
        saveAs: true
      });

      console.log('Chuyển đổi HTML thành PDF thành công');

    } catch (printError) {
      console.error('Lỗi print to PDF:', printError);
      // Fallback: Gửi message đến content script để print
      chrome.tabs.sendMessage(tab.id, {
        action: 'printToPDF',
        fileName: fileName
      });
    }

    // Đóng tab sau 5 giây
    setTimeout(() => {
      chrome.tabs.remove(tab.id);
    }, 5000);

  } catch (error) {
    console.error('Lỗi chuyển đổi HTML thành PDF:', error);
    throw error;
  }
}

// Làm sạch HTML để in PDF tốt hơn
function cleanHTMLForPDF(htmlContent) {
  // Thêm CSS để tối ưu cho PDF
  const pdfCSS = `
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        margin: 20px;
        color: #000;
        background: #fff;
      }
      img { max-width: 100%; height: auto; }
      table { width: 100%; border-collapse: collapse; }
      th, td { border: 1px solid #ddd; padding: 8px; }
      @media print {
        body { margin: 0; }
        .no-print { display: none; }
      }
    </style>
  `;

  // Chèn CSS vào HTML
  if (htmlContent.includes('<head>')) {
    return htmlContent.replace('<head>', '<head>' + pdfCSS);
  } else if (htmlContent.includes('<html>')) {
    return htmlContent.replace('<html>', '<html><head>' + pdfCSS + '</head>');
  } else {
    return `<html><head>${pdfCSS}</head><body>${htmlContent}</body></html>`;
  }
}

// Thử tải xuống với URL cụ thể
function attemptDownload(url, fileName, methodIndex) {
  return new Promise((resolve, reject) => {
    chrome.downloads.download({
      url: url,
      filename: fileName,
      saveAs: true
    }, (downloadId) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        console.log(`Phương pháp ${methodIndex + 1} thành công với ID:`, downloadId);
        resolve(downloadId);
      }
    });
  });
}

// Phương pháp screenshot và chuyển thành PDF
async function tryScreenshotToPDF(fileId, fileName) {
  console.log('Thử phương pháp screenshot to PDF...');

  try {
    // Mở tab với viewer
    const tab = await chrome.tabs.create({
      url: `https://drive.google.com/file/d/${fileId}/view`,
      active: false
    });

    // Đợi trang load
    await new Promise(resolve => setTimeout(resolve, 8000));

    // Inject script để scroll và capture toàn bộ document
    const results = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: captureFullDocument,
      args: [fileName]
    });

    // Đóng tab
    chrome.tabs.remove(tab.id);

    if (results && results[0] && results[0].result) {
      console.log('Screenshot to PDF thành công');
      return true;
    }

  } catch (error) {
    console.error('Lỗi screenshot to PDF:', error);
  }

  // Fallback: Print to PDF
  tryViewerToPDF(fileId, fileName);
}

// Function để capture toàn bộ document
function captureFullDocument(fileName) {
  return new Promise(async (resolve) => {
    try {
      // Tìm PDF viewer container
      const viewer = document.querySelector('[role="main"]') ||
                    document.querySelector('.ndfHFb-c4YZDc-GSWXbd') ||
                    document.querySelector('#drive-viewer-container') ||
                    document.body;

      if (!viewer) {
        resolve(false);
        return;
      }

      // Scroll để load tất cả nội dung
      const scrollHeight = viewer.scrollHeight || document.body.scrollHeight;
      const viewportHeight = window.innerHeight;
      const scrollSteps = Math.ceil(scrollHeight / viewportHeight);

      console.log(`Cần scroll ${scrollSteps} lần để load toàn bộ nội dung`);

      // Scroll từ từ để load content
      for (let i = 0; i < scrollSteps; i++) {
        window.scrollTo(0, i * viewportHeight);
        await new Promise(r => setTimeout(r, 1000));
      }

      // Scroll về đầu
      window.scrollTo(0, 0);
      await new Promise(r => setTimeout(r, 1000));

      // Trigger print
      window.print();

      resolve(true);

    } catch (error) {
      console.log('Lỗi capture document:', error);
      resolve(false);
    }
  });
}

// Phương pháp Print to PDF từ viewer (fallback cuối cùng)
function tryViewerToPDF(fileId, fileName) {
  console.log('Thử phương pháp Print to PDF từ viewer...');

  // Tạo tab để load file và print
  chrome.tabs.create({
    url: `https://drive.google.com/file/d/${fileId}/view`,
    active: false
  }, (tab) => {
    // Đợi tab load xong rồi thực hiện print to PDF
    setTimeout(() => {
      chrome.tabs.sendMessage(tab.id, {
        action: 'printToPDF',
        fileName: fileName
      }, (response) => {
        // Đóng tab sau khi xong
        setTimeout(() => {
          chrome.tabs.remove(tab.id);
        }, 5000);
      });
    }, 5000);
  });
}

// Phương pháp mở tab mới để người dùng tự tải
function openDownloadTab(fileId) {
  const urls = [
    `https://drive.google.com/uc?export=download&id=${fileId}`,
    `https://drive.google.com/file/d/${fileId}/view`,
    `https://drive.google.com/file/d/${fileId}/edit`
  ];

  // Mở tab với URL đầu tiên
  chrome.tabs.create({
    url: urls[0],
    active: true
  });
}

// Kiểm tra tính khả dụng của PDF
async function checkPDFAvailability(fileId) {
  try {
    const response = await fetch(`https://drive.google.com/file/d/${fileId}/view`, {
      method: 'HEAD'
    });
    
    return {
      available: response.ok,
      status: response.status
    };
  } catch (error) {
    return {
      available: false,
      error: error.message
    };
  }
}

// Phương pháp bypass qua Google Cache
async function tryGoogleCacheBypass(fileId, fileName) {
  console.log('Thử bypass qua Google Cache...');

  const cacheUrls = [
    `https://webcache.googleusercontent.com/search?q=cache:drive.google.com/file/d/${fileId}/view`,
    `https://webcache.googleusercontent.com/search?q=cache:docs.google.com/document/d/${fileId}/edit`
  ];

  for (const url of cacheUrls) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        const html = await response.text();
        if (html.includes('pdf') && !html.includes('permission')) {
          // Tìm PDF URL trong cached content
          const pdfMatch = html.match(/https:\/\/[^"]*\.pdf/);
          if (pdfMatch) {
            await attemptDownload(pdfMatch[0], fileName, 0);
            return true;
          }
        }
      }
    } catch (error) {
      console.log('Google Cache bypass thất bại:', error);
    }
  }

  return false;
}

// Phương pháp bypass qua Web Archive
async function tryWebArchiveBypass(fileId, fileName) {
  console.log('Thử bypass qua Web Archive...');

  const archiveUrl = `https://web.archive.org/web/*/https://drive.google.com/file/d/${fileId}/view`;

  try {
    const response = await fetch(archiveUrl);
    if (response.ok) {
      const html = await response.text();
      // Tìm snapshot gần nhất
      const snapshotMatch = html.match(/web\/\d+\/https:\/\/drive\.google\.com\/file\/d\/${fileId}/);
      if (snapshotMatch) {
        const snapshotUrl = `https://web.archive.org/${snapshotMatch[0]}`;
        // Thử tải từ snapshot
        await attemptDownload(snapshotUrl.replace('/view', '/export?format=pdf'), fileName, 0);
        return true;
      }
    }
  } catch (error) {
    console.log('Web Archive bypass thất bại:', error);
  }

  return false;
}

// Phương pháp bypass qua proxy
async function tryProxyBypass(fileId, fileName) {
  console.log('Thử bypass qua proxy...');

  const proxyUrls = [
    `https://cors-anywhere.herokuapp.com/https://drive.google.com/uc?export=download&id=${fileId}`,
    `https://api.allorigins.win/get?url=${encodeURIComponent(`https://drive.google.com/uc?export=download&id=${fileId}`)}`,
    `https://thingproxy.freeboard.io/fetch/https://drive.google.com/uc?export=download&id=${fileId}`
  ];

  for (const url of proxyUrls) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        const blob = await response.blob();
        if (blob.size > 1000 && blob.type.includes('pdf')) {
          const downloadUrl = URL.createObjectURL(blob);
          await chrome.downloads.download({
            url: downloadUrl,
            filename: fileName,
            saveAs: true
          });
          return true;
        }
      }
    } catch (error) {
      console.log('Proxy bypass thất bại:', error);
    }
  }

  return false;
}

// Phương pháp bypass với Mobile User Agent
async function tryMobileUserAgentBypass(fileId, fileName) {
  console.log('Thử bypass với Mobile User Agent...');

  try {
    // Tạo tab với mobile user agent
    const tab = await chrome.tabs.create({
      url: `https://drive.google.com/file/d/${fileId}/view`,
      active: false
    });

    // Inject script để thay đổi user agent
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: () => {
        Object.defineProperty(navigator, 'userAgent', {
          get: () => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
        });

        // Reload trang với mobile user agent
        location.reload();
      }
    });

    // Đợi reload
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Thử tải xuống
    const downloadUrl = `https://drive.google.com/uc?export=download&id=${fileId}`;
    await attemptDownload(downloadUrl, fileName, 0);

    chrome.tabs.remove(tab.id);
    return true;

  } catch (error) {
    console.log('Mobile User Agent bypass thất bại:', error);
    return false;
  }
}

// Phương pháp bypass qua embed
async function tryEmbedBypass(fileId, fileName) {
  console.log('Thử bypass qua embed...');

  const embedUrls = [
    `https://drive.google.com/file/d/${fileId}/preview?embedded=true`,
    `https://docs.google.com/gview?url=https://drive.google.com/file/d/${fileId}/view&embedded=true`,
    `https://drive.google.com/viewerng/viewer?url=https://drive.google.com/file/d/${fileId}/view`
  ];

  for (const url of embedUrls) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        const html = await response.text();

        // Tìm PDF blob URL trong response
        const blobMatch = html.match(/blob:[^"']*/);
        if (blobMatch) {
          await attemptDownload(blobMatch[0], fileName, 0);
          return true;
        }

        // Tìm direct PDF URL
        const pdfMatch = html.match(/https:\/\/[^"']*\.pdf[^"']*/);
        if (pdfMatch) {
          await attemptDownload(pdfMatch[0], fileName, 0);
          return true;
        }
      }
    } catch (error) {
      console.log('Embed bypass thất bại:', error);
    }
  }

  return false;
}

// Xử lý khi extension được cài đặt
chrome.runtime.onInstalled.addListener(() => {
  console.log('Google Drive PDF Downloader đã được cài đặt');
});
