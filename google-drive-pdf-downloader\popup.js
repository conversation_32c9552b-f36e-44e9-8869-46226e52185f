// Popup script cho Google Drive PDF Downloader

document.addEventListener('DOMContentLoaded', async () => {
    const statusDiv = document.getElementById('status');
    const fileInfoDiv = document.getElementById('file-info');
    const fileNameSpan = document.getElementById('file-name');
    const fileIdSpan = document.getElementById('file-id');
    const downloadBtn = document.getElementById('download-btn');
    const copyLinkBtn = document.getElementById('copy-link-btn');
    const messageDiv = document.getElementById('message');
    const helpLink = document.getElementById('help-link');
    const aboutLink = document.getElementById('about-link');

    let currentFileId = null;
    let currentFileName = null;

    // Khởi tạo popup
    await initializePopup();

    // Event listeners
    downloadBtn.addEventListener('click', handleDownload);
    copyLinkBtn.addEventListener('click', handleCopyLink);
    helpLink.addEventListener('click', showHelp);
    aboutLink.addEventListener('click', showAbout);

    // Khởi tạo popup
    async function initializePopup() {
        try {
            // Lấy tab hiện tại
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab) {
                showError('Không thể xác định tab hiện tại');
                return;
            }

            // Kiểm tra xem có phải trang Google Drive không
            if (!isGoogleDriveUrl(tab.url)) {
                showError('Vui lòng mở trang Google Drive chứa file PDF');
                return;
            }

            // Trích xuất file ID từ URL
            currentFileId = extractFileIdFromUrl(tab.url);
            
            if (!currentFileId) {
                showError('Không thể xác định file ID từ URL');
                return;
            }

            // Lấy tên file từ title của tab
            currentFileName = extractFileNameFromTitle(tab.title);

            // Hiển thị thông tin file
            showFileInfo();

            // Kích hoạt các nút
            downloadBtn.disabled = false;
            copyLinkBtn.disabled = false;

            // Ẩn status loading
            statusDiv.style.display = 'none';

        } catch (error) {
            console.error('Lỗi khởi tạo popup:', error);
            showError('Có lỗi xảy ra khi khởi tạo: ' + error.message);
        }
    }

    // Kiểm tra URL Google Drive
    function isGoogleDriveUrl(url) {
        return url && (
            url.includes('drive.google.com/file/d/') ||
            url.includes('docs.google.com/document/d/')
        );
    }

    // Trích xuất file ID từ URL
    function extractFileIdFromUrl(url) {
        const match = url.match(/\/file\/d\/([a-zA-Z0-9-_]+)/);
        return match ? match[1] : null;
    }

    // Trích xuất tên file từ title
    function extractFileNameFromTitle(title) {
        if (!title || title === 'Google Drive') {
            return `google-drive-${currentFileId}.pdf`;
        }
        
        let fileName = title.replace(' - Google Drive', '').trim();
        
        // Đảm bảo có đuôi .pdf
        if (!fileName.toLowerCase().endsWith('.pdf')) {
            fileName += '.pdf';
        }
        
        return fileName;
    }

    // Hiển thị thông tin file
    function showFileInfo() {
        fileNameSpan.textContent = currentFileName;
        fileIdSpan.textContent = `ID: ${currentFileId}`;
        fileInfoDiv.style.display = 'flex';
    }

    // Xử lý tải xuống
    async function handleDownload() {
        if (!currentFileId) {
            showError('Không có file ID để tải xuống');
            return;
        }

        // Hiển thị trạng thái loading
        const originalText = downloadBtn.innerHTML;
        downloadBtn.innerHTML = `
            <div class="spinner" style="width: 16px; height: 16px; border-width: 2px;"></div>
            Đang tải...
        `;
        downloadBtn.disabled = true;

        try {
            // Gửi yêu cầu tải xuống đến background script
            const response = await chrome.runtime.sendMessage({
                action: 'downloadPDF',
                fileId: currentFileId,
                fileName: currentFileName
            });

            if (response && response.success) {
                showSuccess('Bắt đầu tải xuống PDF');

                // Đóng popup sau 2 giây
                setTimeout(() => {
                    window.close();
                }, 2000);
            } else {
                showWarning('Không thể tải xuống trực tiếp. Thử các phương pháp khác...');
                showAlternativeMethods();
            }
        } catch (error) {
            console.error('Lỗi tải xuống:', error);
            showWarning('Lỗi tải xuống. Thử các phương pháp khác...');
            showAlternativeMethods();
        } finally {
            // Khôi phục nút sau 2 giây
            setTimeout(() => {
                downloadBtn.innerHTML = originalText;
                downloadBtn.disabled = false;
            }, 2000);
        }
    }

    // Hiển thị các phương pháp thay thế
    function showAlternativeMethods() {
        const actionsDiv = document.querySelector('.actions');

        // Thêm các nút thay thế
        const alternativeButtons = document.createElement('div');
        alternativeButtons.className = 'alternative-methods';
        alternativeButtons.innerHTML = `
            <div style="margin: 16px 0 8px 0; font-size: 14px; font-weight: 500; color: #5f6368;">
                Phương pháp lách (Bypass):
            </div>
            <button id="bypass-method-btn" class="download-btn" style="margin-bottom: 8px; background: #ea4335;">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                </svg>
                Thử lách qua hạn chế
            </button>
            <button id="print-pdf-btn" class="secondary-btn" style="margin-bottom: 8px;">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z"/>
                </svg>
                In thành PDF
            </button>
            <button id="screenshot-method-btn" class="secondary-btn" style="margin-bottom: 8px;">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z"/>
                </svg>
                Screenshot to PDF
            </button>
            <button id="open-new-tab-btn" class="secondary-btn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
                </svg>
                Mở tab mới
            </button>
        `;

        actionsDiv.appendChild(alternativeButtons);

        // Event listeners cho các nút mới
        document.getElementById('bypass-method-btn').addEventListener('click', async () => {
            showInfo('Đang thử các phương pháp lách...');

            try {
                // Thử các phương pháp bypass nâng cao
                const response = await chrome.runtime.sendMessage({
                    action: 'tryAdvancedBypass',
                    fileId: currentFileId,
                    fileName: currentFileName
                });

                if (response && response.success) {
                    showSuccess('Lách thành công! Đang tải xuống...');
                    setTimeout(() => window.close(), 2000);
                } else {
                    showWarning('Lách thất bại. Thử phương pháp khác...');
                }
            } catch (error) {
                showError('Lỗi khi thử lách: ' + error.message);
            }
        });

        document.getElementById('print-pdf-btn').addEventListener('click', () => {
            // Gửi message đến content script để in
            chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                chrome.tabs.sendMessage(tabs[0].id, {action: 'printPage'});
                window.close();
            });
        });

        document.getElementById('screenshot-method-btn').addEventListener('click', async () => {
            showInfo('Đang chụp màn hình và chuyển thành PDF...');

            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'screenshotToPDF',
                    fileId: currentFileId,
                    fileName: currentFileName
                });

                if (response && response.success) {
                    showSuccess('Screenshot thành công!');
                    setTimeout(() => window.close(), 2000);
                } else {
                    showWarning('Screenshot thất bại');
                }
            } catch (error) {
                showError('Lỗi screenshot: ' + error.message);
            }
        });

        document.getElementById('open-new-tab-btn').addEventListener('click', () => {
            const downloadUrl = `https://drive.google.com/uc?export=download&id=${currentFileId}`;
            chrome.tabs.create({url: downloadUrl});
            window.close();
        });
    }

    // Xử lý sao chép link
    async function handleCopyLink() {
        if (!currentFileId) {
            showError('Không có file ID để sao chép link');
            return;
        }

        const downloadUrl = `https://drive.google.com/uc?export=download&id=${currentFileId}`;

        try {
            await navigator.clipboard.writeText(downloadUrl);
            showSuccess('Đã sao chép link tải xuống');
            
            // Hiệu ứng nút
            const originalText = copyLinkBtn.innerHTML;
            copyLinkBtn.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                </svg>
                Đã sao chép
            `;
            
            setTimeout(() => {
                copyLinkBtn.innerHTML = originalText;
            }, 2000);
            
        } catch (error) {
            console.error('Lỗi sao chép:', error);
            showError('Không thể sao chép link');
        }
    }

    // Hiển thị thông báo thành công
    function showSuccess(message) {
        messageDiv.textContent = message;
        messageDiv.className = 'message success';
        messageDiv.style.display = 'block';
        
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }

    // Hiển thị thông báo lỗi
    function showError(message) {
        statusDiv.style.display = 'none';
        messageDiv.textContent = message;
        messageDiv.className = 'message error';
        messageDiv.style.display = 'block';
    }

    // Hiển thị thông báo cảnh báo
    function showWarning(message) {
        statusDiv.style.display = 'none';
        messageDiv.textContent = message;
        messageDiv.className = 'message warning';
        messageDiv.style.display = 'block';
    }

    // Hiển thị thông báo thông tin
    function showInfo(message) {
        statusDiv.style.display = 'none';
        messageDiv.textContent = message;
        messageDiv.className = 'message info';
        messageDiv.style.display = 'block';
    }

    // Hiển thị trợ giúp
    function showHelp(e) {
        e.preventDefault();
        const helpText = `
Cách sử dụng Google Drive PDF Downloader:

1. Mở file PDF trên Google Drive
2. Nhấp vào icon extension trên thanh công cụ
3. Nhấp "Tải xuống PDF" để tải file
4. Hoặc nhấp "Sao chép link tải" để lấy đường dẫn

Lưu ý:
- Chỉ hoạt động với file PDF
- Cần quyền truy cập file trên Google Drive
- Một số file có thể bị hạn chế tải xuống
        `;
        
        alert(helpText);
    }

    // Hiển thị thông tin
    function showAbout(e) {
        e.preventDefault();
        const aboutText = `
Google Drive PDF Downloader v1.0.0

Tiện ích Chrome giúp tải xuống file PDF từ Google Drive 
ngay cả khi chỉ có quyền xem.

Phát triển bởi: AI Assistant
Phiên bản: 1.0.0
        `;
        
        alert(aboutText);
    }
});
