<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test File Links v<PERSON>i <PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-link {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            font-family: monospace;
            word-break: break-all;
            cursor: pointer;
        }
        .test-link:hover {
            background: #e9ecef;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Extension với File Links có Dấu Cách</h1>
        
        <div class="info">
            <h3>Hướng dẫn test:</h3>
            <ol>
                <li>Đảm bảo extension đã được cài đặt và kích hoạt</li>
                <li>Copy các đường dẫn test dưới đây</li>
                <li>Paste vào Google Sheets</li>
                <li>Kiểm tra xem extension có chuyển đổi đúng không</li>
                <li>Nhấp vào link để test mở file</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>📁 Test Cases - Đường dẫn có dấu cách</h2>
            
            <h3>Case 1: Đường dẫn thực tế của bạn</h3>
            <div class="test-link" onclick="copyToClipboard(this)">
                file:///G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/20250530.330.TB-TTĐT Thay đổi cán bộ, thời gian, địa điểm khóa đào tạo vận hành hệ thống Intellect.pdf
            </div>
            
            <h3>Case 2: Đường dẫn Windows thông thường</h3>
            <div class="test-link" onclick="copyToClipboard(this)">
                file:///C:/Program Files/Microsoft Office/Office16/WINWORD.EXE
            </div>
            
            <div class="test-link" onclick="copyToClipboard(this)">
                file:///C:/Users/<USER>/Documents/My Document.docx
            </div>
            
            <h3>Case 3: Đường dẫn có ký tự đặc biệt</h3>
            <div class="test-link" onclick="copyToClipboard(this)">
                file:///D:/Projects/Web Development/My Project (2024)/index.html
            </div>
            
            <div class="test-link" onclick="copyToClipboard(this)">
                file:///C:/Users/<USER>/Desktop/Báo cáo tháng 12-2024.xlsx
            </div>
            
            <h3>Case 4: Đường dẫn dài</h3>
            <div class="test-link" onclick="copyToClipboard(this)">
                file:///E:/Very Long Folder Name With Many Spaces/Subfolder With More Spaces/Another Level/Final Document Name With Spaces.pdf
            </div>
            
            <h3>Case 5: Nhiều extensions khác nhau</h3>
            <div class="test-link" onclick="copyToClipboard(this)">
                file:///C:/Documents/Presentation File.pptx
            </div>
            
            <div class="test-link" onclick="copyToClipboard(this)">
                file:///D:/Images/Photo Album 2024.jpg
            </div>
            
            <div class="test-link" onclick="copyToClipboard(this)">
                file:///C:/Videos/Tutorial Video.mp4
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Test Regex Locally</h2>
            <p>Test regex trực tiếp trên trang này:</p>
            
            <textarea id="testInput" placeholder="Nhập text chứa file links để test..." style="width: 100%; height: 100px; margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
            
            <button onclick="testRegex()">Test Regex</button>
            <button onclick="clearResults()">Clear</button>
            
            <div id="regexResults"></div>
        </div>
        
        <div class="test-section">
            <h2>📋 Kết quả Test</h2>
            <div id="testResults">
                <p>Chưa có kết quả test nào...</p>
            </div>
            
            <button onclick="clearTestResults()">Clear Results</button>
        </div>
    </div>

    <script>
        let testCount = 0;
        
        function copyToClipboard(element) {
            const text = element.textContent.trim();
            navigator.clipboard.writeText(text).then(() => {
                const originalBg = element.style.background;
                element.style.background = '#d4edda';
                element.innerHTML = '✅ Đã copy! ' + text;
                
                setTimeout(() => {
                    element.style.background = originalBg;
                    element.innerHTML = text;
                }, 2000);
                
                logTestResult(`Đã copy: ${text.substring(0, 50)}...`, 'info');
            }).catch(err => {
                logTestResult(`Lỗi copy: ${err.message}`, 'error');
            });
        }
        
        function testRegex() {
            const input = document.getElementById('testInput').value;
            if (!input.trim()) {
                alert('Vui lòng nhập text để test!');
                return;
            }
            
            // Sử dụng regex giống như trong extension
            const fileLinkRegex = /file:\/\/\/[^\r\n"'<>|]*?(?:\.[a-zA-Z0-9]+|\/)?(?=\s|$|[.,;:!?](?:\s|$)|")/gi;
            const simpleRegex = /file:\/\/\/[A-Za-z]:[^\r\n"'<>|]*\.[a-zA-Z0-9]+/gi;
            
            const matches1 = input.match(fileLinkRegex) || [];
            const matches2 = input.match(simpleRegex) || [];
            
            // Combine và deduplicate
            const allMatches = [...new Set([...matches1, ...matches2])];
            
            // Filter như trong extension
            const filteredMatches = allMatches.map(match => {
                return match.replace(/[.,;:!?\s]+$/, '').trim();
            }).filter(match => {
                return match.length > 8 &&
                       match.startsWith('file:///') &&
                       (match.includes('.') || match.endsWith('/')) &&
                       !match.includes('..') &&
                       /file:\/\/\/[A-Za-z]:/.test(match);
            });
            
            const resultsDiv = document.getElementById('regexResults');
            resultsDiv.innerHTML = `
                <div class="result info">
                    <h4>Kết quả Test Regex:</h4>
                    <p><strong>Input:</strong> ${input}</p>
                    <p><strong>Matches tìm thấy:</strong> ${filteredMatches.length}</p>
                    ${filteredMatches.map((match, i) => `<p>${i + 1}. ${match}</p>`).join('')}
                </div>
            `;
            
            logTestResult(`Regex test: Tìm thấy ${filteredMatches.length} matches`, 'success');
        }
        
        function clearResults() {
            document.getElementById('regexResults').innerHTML = '';
            document.getElementById('testInput').value = '';
        }
        
        function logTestResult(message, type) {
            testCount++;
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultElement = document.createElement('div');
            resultElement.className = `result ${type}`;
            resultElement.innerHTML = `<strong>[${testCount}] ${timestamp}:</strong> ${message}`;
            
            if (resultsDiv.children.length === 1 && resultsDiv.children[0].textContent.includes('Chưa có kết quả')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.appendChild(resultElement);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearTestResults() {
            document.getElementById('testResults').innerHTML = '<p>Chưa có kết quả test nào...</p>';
            testCount = 0;
        }
        
        // Auto-populate test input với case phức tạp
        document.addEventListener('DOMContentLoaded', () => {
            const testInput = document.getElementById('testInput');
            testInput.value = `Đây là test với nhiều file links:
file:///G:/My Drive/2. Cá nhân/6. Test viết code/Dự án đọc tài liệu/20250530.330.TB-TTĐT Thay đổi cán bộ, thời gian, địa điểm khóa đào tạo vận hành hệ thống Intellect.pdf
và file:///C:/Program Files/Microsoft Office/Office16/WINWORD.EXE
cũng như file:///D:/Projects/Web Development/My Project (2024)/index.html`;
        });
    </script>
</body>
</html>
